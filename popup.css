* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    width: 420px;
    height: 600px;
    overflow: hidden;
    background: #f8fafc;
}

.app-container {
    display: flex;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

/* Sidebar Navigation */
.sidebar {
    width: 70px;
    background: linear-gradient(180deg, #4f46e5 0%, #7c3aed 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;
    position: relative;
}

.sidebar-header {
    margin-bottom: 30px;
}

.app-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.app-icon svg {
    width: 24px;
    height: 24px;
    color: white;
}

.sidebar-nav {
    display: flex;
    flex-direction: column;
    gap: 16px;
    flex: 1;
}

.sidebar-footer {
    margin-top: auto;
}

.nav-btn {
    width: 50px;
    height: 50px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.nav-btn.active {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.nav-icon {
    font-size: 20px;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-icon svg {
    width: 20px;
    height: 20px;
    color: white;
    transition: all 0.3s ease;
}

/* Main Content Area */
.main-content {
    flex: 1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.page {
    display: none;
    height: 100%;
    padding: 15px 20px;
    overflow-y: auto;
    color: white;
}

/* History page specific layout */
#history-page {
    display: none;
    height: 100%;
    padding: 15px 20px;
    overflow: hidden; /* No external scrolling */
    color: white;
    flex-direction: column;
}

#history-page.active {
    display: flex; /* Use flexbox for proper layout */
}

.page.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

.page-header {
    text-align: center;
    margin-bottom: 20px;
}

.page-header h2 {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 6px;
    color: white;
}

.page-header p {
    font-size: 12px;
    opacity: 0.9;
    color: rgba(255, 255, 255, 0.8);
}

/* Connection Status (VPN-style) */
.connection-status {
    text-align: center;
    margin-bottom: 25px;
}

.status-circle {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
}

.status-icon {
    font-size: 36px;
    color: white;
    opacity: 0.9;
    display: flex;
    align-items: center;
    justify-content: center;
}

.status-icon svg {
    width: 36px;
    height: 36px;
    color: white;
}

.status-text {
    font-size: 14px;
    font-weight: 600;
    color: white;
    opacity: 0.9;
}

/* Main Stats */
.main-stats {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.info-card {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 12px;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    min-width: 140px;
    max-width: 160px;
    flex: 1;
}

.info-card:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.info-icon {
    font-size: 22px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: white;
}

.info-content {
    flex: 1;
}

.info-number {
    font-size: 18px;
    font-weight: 700;
    color: white;
    line-height: 1;
}

.info-label {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
    margin-top: 3px;
}

.info-value {
    font-size: 9px;
    font-weight: 600;
    color: white;
    line-height: 1.2;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 4px;
    border-radius: 3px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100px;
}

/* Current Status */
.current-status {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.status-item:last-child {
    border-bottom: none;
}

.status-label {
    font-size: 12px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
}

.status-value {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.7);
    background: rgba(255, 255, 255, 0.1);
    padding: 4px 8px;
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 60%;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Main Actions */
.main-actions {
    display: flex;
    justify-content: center;
    gap: 12px;
}

.main-transfer-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    backdrop-filter: blur(10px);
    min-width: 160px;
    flex: 1;
}

.main-transfer-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.transfer-icon {
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.transfer-icon svg {
    width: 18px;
    height: 18px;
    color: white;
}

.transfer-text {
    font-size: 14px;
}

.auto-transfer-btn {
    min-width: 80px;
    height: 48px;
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    flex-shrink: 0;
    gap: 2px;
    padding: 6px 8px;
    position: relative;
}

.auto-transfer-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.auto-transfer-btn.active {
    background: rgba(16, 185, 129, 0.3);
    border-color: rgba(16, 185, 129, 0.6);
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
}

.auto-transfer-btn.active:hover {
    background: rgba(16, 185, 129, 0.4);
    border-color: rgba(16, 185, 129, 0.8);
}

.auto-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1px;
}

.auto-icon svg {
    width: 14px;
    height: 14px;
    color: white;
}

.auto-label {
    font-size: 8px;
    font-weight: 700;
    color: white;
    line-height: 1;
    letter-spacing: 0.5px;
}

.auto-status {
    font-size: 7px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1;
    letter-spacing: 0.3px;
}

.auto-transfer-btn.active .auto-status {
    color: rgba(16, 185, 129, 1);
}

/* Toast Notification */
.toast {
    position: fixed;
    top: -60px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
    z-index: 1000;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
    backdrop-filter: blur(10px);
}

.toast.show {
    top: 20px;
    opacity: 1;
}

.toast.error {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    box-shadow: 0 4px 16px rgba(239, 68, 68, 0.3);
}

.toast.warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 4px 16px rgba(245, 158, 11, 0.3);
}

/* History Page Styles */
.history-total {
    width: 100%;
    margin-bottom: 15px;
    flex-shrink: 0; /* Don't shrink */
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.action-btn {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 10px 16px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    backdrop-filter: blur(10px);
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.action-btn.clear {
    border-color: rgba(239, 68, 68, 0.5);
}

.history-stat {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 10px 12px;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 11px;
    color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-icon {
    font-size: 14px;
}

.history-stat strong {
    color: white;
}

.history-list {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 20px;
    flex: 1; /* Take up remaining height */
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    overflow-y: auto; /* Internal scrolling only */
    margin-bottom: 15px;
}

/* Scrollbar styling for history list */
.history-list::-webkit-scrollbar {
    width: 6px;
}

.history-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 160px;
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 12px;
    opacity: 0.6;
}

.empty-state p {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.8);
}

.empty-state small {
    font-size: 13px;
    opacity: 0.8;
}

/* History footer positioning */
.history-footer {
    flex-shrink: 0; /* Don't shrink */
    display: flex;
    justify-content: center;
    width: 100%;
}

/* History item styles */
.history-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 12px;
    margin-bottom: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.2s ease;
}

.history-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.history-item:last-child {
    margin-bottom: 0;
}

.history-item.success {
    border-left: 3px solid rgba(16, 185, 129, 0.8);
}

.history-item.warning {
    border-left: 3px solid rgba(245, 158, 11, 0.8);
}

.history-item.error {
    border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.history-status {
    font-size: 14px;
}

.history-time {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.7);
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.history-details {
    font-size: 11px;
    line-height: 1.4;
}

.history-variables,
.history-from,
.history-to,
.history-value {
    margin-bottom: 4px;
    color: rgba(255, 255, 255, 0.9);
}

.history-variables:last-child,
.history-from:last-child,
.history-to:last-child,
.history-value:last-child {
    margin-bottom: 0;
}

.history-variables {
    font-weight: 600;
    color: rgba(255, 255, 255, 0.95);
    font-size: 12px;
}

.history-from,
.history-to {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.8);
    padding-left: 8px;
}

.history-value {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 10px;
    background: rgba(255, 255, 255, 0.1);
    padding: 3px 6px;
    border-radius: 4px;
    display: inline-block;
    margin-top: 2px;
}

.history-error {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.95);
    font-weight: 600;
    margin-bottom: 6px;
}

.history-error-reason {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.85);
    background: rgba(255, 255, 255, 0.08);
    padding: 8px 10px;
    border-radius: 6px;
    border-left: 3px solid rgba(255, 255, 255, 0.3);
    word-break: break-word;
    line-height: 1.4;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.15);
}

/* Configuration Page Styles */
.config-section {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.config-section h4 {
    margin: 0 0 20px 0;
    font-size: 16px;
    font-weight: 600;
    color: white;
    display: flex;
    align-items: center;
    gap: 8px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

/* Variables Header */
.variables-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.variables-header h4 {
    margin: 0;
    padding: 0;
    border: none;
}

.header-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* Element Hunter Button */
.element-hunter-btn {
    width: 36px;
    height: 36px;
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.element-hunter-btn svg {
    width: 18px;
    height: 18px;
    color: white;
    transition: all 0.3s ease;
}

.element-hunter-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 38, 0.2));
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 10px;
}

.element-hunter-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.element-hunter-btn:hover::before {
    opacity: 1;
}

.element-hunter-btn:active {
    transform: translateY(-1px) scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.element-hunter-btn.active {
    background: rgba(239, 68, 68, 0.3);
    border-color: rgba(239, 68, 68, 0.6);
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
}

.element-hunter-btn.active:hover {
    background: rgba(239, 68, 68, 0.4);
    border-color: rgba(239, 68, 68, 0.8);
}

.element-hunter-btn.active svg {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Add Variable Button */
.add-variable-btn {
    width: 36px;
    height: 36px;
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    font-size: 18px;
    font-weight: 300;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.add-variable-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(5, 150, 105, 0.2));
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 10px;
}

.add-variable-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.add-variable-btn:hover::before {
    opacity: 1;
}

.add-variable-btn:active {
    transform: translateY(-1px) scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Variables List */
.variables-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.variable-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
}

.variable-item:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
}

.variable-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.variable-number {
    font-size: 12px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.8);
    background: rgba(255, 255, 255, 0.1);
    padding: 4px 8px;
    border-radius: 6px;
}

.remove-variable-btn {
    background: rgba(239, 68, 68, 0.2);
    color: white;
    border: 1px solid rgba(239, 68, 68, 0.5);
    border-radius: 6px;
    padding: 4px 8px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.remove-variable-btn:hover {
    background: rgba(239, 68, 68, 0.3);
    border-color: rgba(239, 68, 68, 0.7);
    transform: scale(1.05);
}

.variable-inputs {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.variable-input-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.variable-input-group label {
    font-size: 11px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
}

.variable-input-group input {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    font-size: 12px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    backdrop-filter: blur(10px);
}

.variable-input-group input::placeholder {
    color: rgba(255, 255, 255, 0.5);
    font-size: 11px;
}

.variable-input-group input:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

.input-group {
    margin-bottom: 20px;
}

.input-group label {
    display: block;
    font-weight: 600;
    color: white;
    margin-bottom: 8px;
    font-size: 14px;
}

.input-group input {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    backdrop-filter: blur(10px);
}

.input-group input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.input-group input:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.help-text {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 5px;
    line-height: 1.4;
}

/* Settings Page Styles */
.setting-group {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 12px;
    margin-bottom: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.2s ease;
}

.setting-group:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
}

.setting-label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    color: white;
    margin-bottom: 4px;
}

.setting-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 16px;
    height: 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    position: relative;
    transition: all 0.2s ease;
}

.setting-label input[type="checkbox"]:checked + .checkmark {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

.setting-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 10px;
    font-weight: bold;
}

.setting-group label:not(.setting-label) {
    display: block;
    font-size: 12px;
    font-weight: 500;
    color: white;
    margin-bottom: 6px;
}

.setting-group select {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    font-size: 11px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
}

.setting-group select:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.15);
}

.setting-group select option {
    background: #4f46e5;
    color: white;
}

.setting-description {
    display: block;
    font-size: 10px;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 4px;
    line-height: 1.3;
}

/* Radio Button Group */
.radio-group {
    display: flex;
    gap: 15px;
    margin-bottom: 8px;
}

.radio-option {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    color: white;
    transition: all 0.2s ease;
}

.radio-option:hover {
    color: rgba(255, 255, 255, 0.9);
}

.radio-option input[type="radio"] {
    display: none;
}

.radio-checkmark {
    width: 16px;
    height: 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    position: relative;
    transition: all 0.2s ease;
}

.radio-option input[type="radio"]:checked + .radio-checkmark {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.6);
}

.radio-option input[type="radio"]:checked + .radio-checkmark::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 6px;
    height: 6px;
    background: white;
    border-radius: 50%;
}

/* Mode Info Sections */
.mode-info {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    animation: slideDown 0.3s ease-out;
}

.mode-details {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.mode-details h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: 600;
    color: white;
    display: flex;
    align-items: center;
    gap: 8px;
}

.mode-details p {
    margin: 0 0 12px 0;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
}

.mode-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mode-features li {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    gap: 6px;
}

/* Transfer Trigger Options */
.trigger-options {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    animation: slideDown 0.3s ease-out;
}

.trigger-info {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.trigger-info h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: 600;
    color: white;
    display: flex;
    align-items: center;
    gap: 8px;
}

.trigger-info p {
    margin: 0 0 12px 0;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
}

.trigger-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.trigger-features li {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    gap: 6px;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
        max-height: 0;
    }
    to {
        opacity: 1;
        transform: translateY(0);
        max-height: 200px;
    }
}

/* Tab Selection Styles */

.tab-containers {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 15px;
}

.tab-container {
    width: 100%;
    border: 2px dashed rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    min-height: 65px;
}

.source-container {
    border-color: rgba(16, 185, 129, 0.5);
    background: rgba(16, 185, 129, 0.1);
}

.destination-container {
    border-color: rgba(239, 68, 68, 0.5);
    background: rgba(239, 68, 68, 0.1);
}

.tab-container.drag-over {
    border-color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 16px rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.tab-slot {
    padding: 15px;
    min-height: 55px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.slot-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.7);
    text-align: center;
    opacity: 0.8;
}

.placeholder-icon {
    font-size: 18px;
    color: rgba(255, 255, 255, 0.8);
}

.placeholder-text {
    font-size: 11px;
    font-weight: 500;
    letter-spacing: 0.2px;
    color: rgba(255, 255, 255, 0.7);
}

.selected-tab {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 12px 40px 12px 12px;
    position: relative;
    cursor: pointer;
    backdrop-filter: blur(10px);
    transition: all 0.2s ease;
    width: 100%;
    display: flex;
    align-items: center;
    gap: 10px;
}

.selected-tab:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-1px);
}

.tab-arrow {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    min-width: 40px;
}

.arrow-icon {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
}

.arrow-text {
    font-size: 8px;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.7);
    letter-spacing: 0.5px;
}

.selected-tab-title {
    font-weight: 600;
    color: white;
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
}

.selected-tab .remove-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(239, 68, 68, 0.8);
    color: white;
    border: none;
    border-radius: 50%;
    width: 22px;
    height: 22px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    transition: all 0.2s ease;
}

.selected-tab .remove-btn:hover {
    background: rgba(239, 68, 68, 1);
    transform: scale(1.1);
}

.available-tabs {
    margin-top: 15px;
    border-radius: 10px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.tabs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px;
    background: rgba(255, 255, 255, 0.1);
    font-size: 11px;
    font-weight: 600;
    color: white;
    letter-spacing: 0.3px;
    backdrop-filter: blur(10px);
}

.header-buttons {
    display: flex;
    gap: 6px;
}

.refresh-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    cursor: pointer;
    font-size: 10px;
    padding: 4px 6px;
    border-radius: 4px;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
}

.refresh-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.tabs-list {
    max-height: 120px;
    overflow-y: auto;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
}

.tabs-list::-webkit-scrollbar {
    width: 6px;
}

.tabs-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.tabs-list::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.tabs-list::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

.tab-item {
    padding: 10px 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 10px;
    line-height: 1.4;
    cursor: grab;
    transition: all 0.2s ease;
    user-select: none;
    position: relative;
}

.tab-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(4px);
}

.tab-item:active {
    cursor: grabbing;
    transform: scale(0.98);
}

.tab-item:last-child {
    border-bottom: none;
}

.tab-item.dragging {
    opacity: 0.6;
    transform: rotate(2deg) scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.tab-title {
    font-weight: 600;
    color: white;
    margin-bottom: 3px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 11px;
}

.tab-url {
    color: rgba(255, 255, 255, 0.7);
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 9px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 4px;
    border-radius: 3px;
    display: inline-block;
}

/* Help Page Styles */
.help-section {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

details {
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

summary {
    padding: 16px 20px;
    background: rgba(255, 255, 255, 0.1);
    cursor: pointer;
    font-weight: 600;
    font-size: 14px;
    color: white;
    user-select: none;
    transition: all 0.2s ease;
}

summary:hover {
    background: rgba(255, 255, 255, 0.15);
}

.help-content {
    padding: 0;
    font-size: 11px;
    line-height: 1.5;
    background: transparent;
}

.help-content h3 {
    font-size: 14px;
    font-weight: 600;
    color: white;
    margin-bottom: 12px;
}

.help-content h4 {
    font-size: 12px;
    font-weight: 600;
    color: white;
    margin: 10px 0 6px 0;
}

.help-content ul {
    margin-left: 15px;
    margin-bottom: 12px;
}

.help-content li {
    margin-bottom: 4px;
    color: rgba(255, 255, 255, 0.9);
}

.help-content code {
    background: rgba(255, 255, 255, 0.15);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 10px;
    color: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Animations */
@keyframes fadeIn {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Page scrollbar */
.page::-webkit-scrollbar {
    width: 6px;
}

.page::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.page::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.page::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Loading animation for transfer button */
.main-transfer-btn.loading {
    position: relative;
    color: transparent;
}

.main-transfer-btn.loading::after {
    content: '';
    position: absolute;
    width: 22px;
    height: 22px;
    border: 3px solid transparent;
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Element Selection Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 24px;
    width: 90%;
    max-width: 400px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: slideUp 0.3s ease;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-header h3 {
    margin: 0;
    color: white;
    font-size: 18px;
    font-weight: 600;
}

.close-modal-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 300;
    transition: all 0.3s ease;
}

.close-modal-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.modal-body {
    color: white;
}

.captured-element-info {
    margin-bottom: 20px;
}

.element-preview {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.element-tag {
    display: inline-block;
    background: rgba(16, 185, 129, 0.3);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    margin-right: 8px;
}

.element-selector {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.9);
    background: rgba(255, 255, 255, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
}

.selector-options {
    margin-bottom: 15px;
}

.selector-options label {
    display: block;
    font-size: 12px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 8px;
}

.selector-options select,
.selector-options input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 12px;
    backdrop-filter: blur(10px);
    margin-bottom: 8px;
}

.selector-options select:focus,
.selector-options input:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.15);
}

.selector-options input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.variable-selection label {
    display: block;
    font-size: 12px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 12px;
}

.field-options {
    display: flex;
    gap: 10px;
}

.field-option-btn {
    flex: 1;
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 10px 16px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.field-option-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.field-option-btn.selected {
    background: rgba(16, 185, 129, 0.3);
    border-color: rgba(16, 185, 129, 0.6);
    box-shadow: 0 0 15px rgba(16, 185, 129, 0.3);
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Tab item drag feedback */
.tab-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: rgba(255, 255, 255, 0.5);
    transform: scaleY(0);
    transition: transform 0.2s ease;
}

.tab-item:hover::before {
    transform: scaleY(1);
}

/* File Mode Styles */
.file-mode-page {
    display: none !important;
}

.file-mode-page.active {
    display: block !important;
}

/* File Upload Area */
.file-upload-area {
    margin-bottom: 20px;
}

.file-drop-zone {
    border: 2px dashed rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 30px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
}

.file-drop-zone:hover {
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.file-drop-zone.drag-over {
    border-color: rgba(16, 185, 129, 0.8);
    background: rgba(16, 185, 129, 0.1);
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
}

.drop-zone-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.drop-icon {
    font-size: 32px;
    opacity: 0.8;
}

.drop-text {
    font-size: 14px;
    font-weight: 600;
    color: white;
}

.drop-hint {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.7);
}

/* File Info */
.file-info {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.file-details {
    display: flex;
    align-items: center;
    gap: 12px;
}

.file-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
}

.file-content {
    flex: 1;
}

.file-name {
    font-size: 14px;
    font-weight: 600;
    color: white;
    margin-bottom: 4px;
}

.file-size {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.7);
}

.remove-file-btn {
    width: 30px;
    height: 30px;
    border: none;
    background: rgba(239, 68, 68, 0.2);
    color: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.remove-file-btn:hover {
    background: rgba(239, 68, 68, 0.4);
    transform: scale(1.1);
}

/* File Preview */
.file-preview {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.preview-table {
    max-height: 200px;
    overflow: auto;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 10px;
    margin-top: 10px;
}

.preview-table table {
    width: 100%;
    border-collapse: collapse;
    font-size: 11px;
}

.preview-table th,
.preview-table td {
    padding: 6px 8px;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: white;
}

.preview-table th {
    background: rgba(255, 255, 255, 0.1);
    font-weight: 600;
    position: sticky;
    top: 0;
}

/* Mapping List */
.mapping-list {
    margin-bottom: 20px;
}

.mapping-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.mapping-row {
    display: flex;
    align-items: center;
    gap: 12px;
}

.mapping-select {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 8px 12px;
    color: white;
    font-size: 12px;
}

.mapping-select option {
    background: #4f46e5;
    color: white;
}

.mapping-arrow {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.7);
}

.mapping-input {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 8px 12px;
    color: white;
    font-size: 12px;
}

.mapping-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.remove-mapping-btn {
    width: 30px;
    height: 30px;
    border: none;
    background: rgba(239, 68, 68, 0.2);
    color: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.remove-mapping-btn:hover {
    background: rgba(239, 68, 68, 0.4);
    transform: scale(1.1);
}

/* Mapping Actions */
.mapping-actions {
    text-align: center;
    margin-bottom: 20px;
}

.add-mapping-btn {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 10px 20px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    backdrop-filter: blur(10px);
    margin: 0 auto;
}

.add-mapping-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.add-mapping-btn span {
    font-size: 16px;
    font-weight: bold;
}

/* File Stats */
.file-stats {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-top: 10px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-label {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.stat-value {
    font-size: 12px;
    color: white;
    font-weight: 600;
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
}

/* Columns Display */
.columns-display {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.columns-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
    max-height: 120px;
    overflow-y: auto;
}

.column-chip {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    cursor: pointer;
    user-select: none;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);
}

.column-chip:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.column-chip.selected {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Preview Controls */
.preview-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    padding: 8px 0;
}

.preview-controls label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.sheet-selector {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 6px 10px;
    color: white;
    font-size: 11px;
    min-width: 120px;
}

.sheet-selector option {
    background: #4f46e5;
    color: white;
}

/* Enhanced Preview Table */
.preview-table {
    max-height: 150px;
    overflow: auto;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 8px;
    margin-top: 10px;
}

.preview-table table {
    width: 100%;
    border-collapse: collapse;
    font-size: 10px;
}

.preview-table th {
    background: rgba(16, 185, 129, 0.2);
    color: white;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 1;
    border-bottom: 2px solid rgba(16, 185, 129, 0.4);
}

.preview-table th,
.preview-table td {
    padding: 4px 6px;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: white;
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.preview-table tr:nth-child(even) {
    background: rgba(255, 255, 255, 0.02);
}

.preview-table tr:hover {
    background: rgba(255, 255, 255, 0.05);
}
