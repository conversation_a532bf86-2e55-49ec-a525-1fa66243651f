// Ant DatePicker Popup Automation Script - Your Working Version
// This simulates the exact manual process: click field -> popup opens -> type in popup -> press enter

(function() {
    'use strict';

    // Main function to detect and handle Ant Design date pickers
    window.PageMateAntDatePicker = {
        
        // Check if an element is an Ant Design date picker
        isAntDatePicker: function(element) {
            if (!element) return false;

            console.log('🗓️ Checking element for Ant Design date picker:', element);
            console.log('🗓️ Element ID:', element.id);
            console.log('🗓️ Element classes:', element.className);
            console.log('🗓️ Element placeholder:', element.placeholder);

            // Check for Ant Design date picker classes and attributes
            const antClasses = [
                'ant-calendar-picker',
                'ant-calendar-picker-input',
                'ant-date-picker',
                'ant-picker',
                'ant-picker-input'
            ];

            // Check element itself
            for (const className of antClasses) {
                if (element.classList.contains(className)) {
                    console.log('🗓️ Detected Ant Design date picker by class:', className);
                    return true;
                }
            }

            // Check parent elements (up to 3 levels)
            let parent = element.parentElement;
            let level = 0;
            while (parent && level < 3) {
                for (const className of antClasses) {
                    if (parent.classList.contains(className)) {
                        console.log('🗓️ Detected Ant Design date picker in parent by class:', className);
                        return true;
                    }
                }
                parent = parent.parentElement;
                level++;
            }

            // Check for placeholder patterns that suggest date picker
            const placeholder = element.placeholder || '';
            const datePatterns = ['DD-MM-YYYY', 'MM/DD/YYYY', 'YYYY-MM-DD', 'Select date', 'datePicker'];
            if (datePatterns.some(pattern => placeholder.includes(pattern))) {
                console.log('🗓️ Detected potential date picker by placeholder:', placeholder);
                return true;
            }

            // Check for ID patterns that suggest date picker
            const elementId = element.id || '';
            const idPatterns = ['datePicker', 'date-picker', 'calendar', 'expire'];
            if (idPatterns.some(pattern => elementId.toLowerCase().includes(pattern.toLowerCase()))) {
                console.log('🗓️ Detected potential date picker by ID pattern:', elementId);
                return true;
            }

            console.log('🗓️ No Ant Design date picker detected');
            return false;
        },

        // Main function - your working script
        setDateValue: function(element, dateString = '31-03-2028') {
            console.log('🗓️ Starting Ant DatePicker automation for value:', dateString);
            
            return new Promise((resolve, reject) => {
                try {
                    // Find the main input element
                    let mainInput = element;
                    
                    // If element is not an input, try to find input within the picker
                    if (element.tagName.toLowerCase() !== 'input') {
                        const inputInPicker = element.querySelector('input');
                        if (inputInPicker) {
                            mainInput = inputInPicker;
                        }
                    }
                    
                    if (!mainInput || mainInput.tagName.toLowerCase() !== 'input') {
                        throw new Error('Could not find input element in date picker');
                    }
                    
                    console.log('🗓️ Found main input element:', mainInput);
                    
                    // Step 1: Click the main date field to open popup
                    this.setAntDatePickerViaPopup(mainInput, dateString)
                        .then(() => {
                            console.log('✅ Ant DatePicker automation completed successfully');
                            resolve(true);
                        })
                        .catch((error) => {
                            console.error('❌ Ant DatePicker automation failed:', error);
                            reject(error);
                        });
                        
                } catch (error) {
                    console.error('❌ Ant DatePicker automation error:', error);
                    reject(error);
                }
            });
        },

        // Your exact working script
        setAntDatePickerViaPopup: function(mainInput, dateString = '31-03-2028') {
            return new Promise((resolve, reject) => {
                if (!mainInput) {
                    console.log('Main date input not found');
                    reject(new Error('Main date input not found'));
                    return;
                }
                
                console.log('Step 1: Clicking main input to open popup...');
                
                // Click to open the popup
                mainInput.click();
                mainInput.focus();
                
                // Wait for popup to appear, then interact with it
                setTimeout(() => {
                    this.interactWithPopup(dateString)
                        .then(resolve)
                        .catch(reject);
                }, 200);
            });
        },

        interactWithPopup: function(dateString) {
            return new Promise((resolve, reject) => {
                console.log('Step 2: Looking for popup input field...');
                
                // Look for the input field inside the popup calendar
                const popupSelectors = [
                    '.ant-calendar-input',
                    '.ant-calendar-date-input-wrap input',
                    '.ant-calendar-panel input',
                    '.ant-calendar-picker-container input'
                ];
                
                let popupInput = null;
                for (const selector of popupSelectors) {
                    popupInput = document.querySelector(selector);
                    if (popupInput && popupInput.offsetParent !== null) { // Check if visible
                        console.log('Found popup input with selector:', selector);
                        break;
                    }
                }
                
                if (!popupInput) {
                    console.log('Popup input not found, trying alternative approach...');
                    // Sometimes the popup takes longer to render
                    setTimeout(() => {
                        this.interactWithPopup(dateString)
                            .then(resolve)
                            .catch(reject);
                    }, 300);
                    return;
                }
                
                console.log('Step 3: Setting date in popup input field...');
                
                // Clear and focus the popup input
                popupInput.focus();
                popupInput.value = '';
                
                // Set the value directly
                popupInput.value = dateString;
                
                // Trigger input event
                const inputEvent = new Event('input', { 
                    bubbles: true, 
                    cancelable: true 
                });
                popupInput.dispatchEvent(inputEvent);
                
                // Press Enter after a short delay
                setTimeout(() => {
                    this.pressEnterInPopup(popupInput)
                        .then(resolve)
                        .catch(reject);
                }, 100);
            });
        },

        pressEnterInPopup: function(popupInput) {
            return new Promise((resolve, reject) => {
                console.log('Step 4: Pressing Enter to confirm date...');
                
                // Simulate pressing Enter key
                const enterKeyDown = new KeyboardEvent('keydown', {
                    key: 'Enter',
                    code: 'Enter',
                    keyCode: 13,
                    which: 13,
                    bubbles: true,
                    cancelable: true
                });
                
                const enterKeyPress = new KeyboardEvent('keypress', {
                    key: 'Enter',
                    code: 'Enter',
                    keyCode: 13,
                    which: 13,
                    bubbles: true,
                    cancelable: true
                });
                
                const enterKeyUp = new KeyboardEvent('keyup', {
                    key: 'Enter',
                    code: 'Enter',
                    keyCode: 13,
                    which: 13,
                    bubbles: true,
                    cancelable: true
                });
                
                // Dispatch Enter key events
                popupInput.dispatchEvent(enterKeyDown);
                popupInput.dispatchEvent(enterKeyPress);
                popupInput.dispatchEvent(enterKeyUp);
                
                // Also trigger change and blur events
                setTimeout(() => {
                    const changeEvent = new Event('change', { bubbles: true, cancelable: true });
                    popupInput.dispatchEvent(changeEvent);
                    
                    const blurEvent = new Event('blur', { bubbles: true, cancelable: true });
                    popupInput.dispatchEvent(blurEvent);
                    
                    console.log('Date setting complete!');
                    
                    // Verify the date was set
                    setTimeout(() => {
                        this.verifyDateWasSet()
                            .then(resolve)
                            .catch(reject);
                    }, 500);
                }, 100);
            });
        },

        verifyDateWasSet: function() {
            return new Promise((resolve) => {
                const mainInput = document.querySelector('input[placeholder="DD-MM-YYYY"]') || 
                                document.querySelector('input[id*="datePicker"]') ||
                                document.querySelector('input[id*="expire"]');
                                
                if (mainInput && mainInput.value) {
                    console.log('✅ Success! Date is set to:', mainInput.value);
                    resolve();
                } else {
                    console.log('⚠️ Date might not have been set properly');
                    
                    // Try alternative approach - click a date in the calendar
                    setTimeout(() => {
                        this.clickDateInCalendar('31')
                            .then(resolve)
                            .catch(() => resolve()); // Don't fail, just continue
                    }, 200);
                }
            });
        },

        clickDateInCalendar: function(day) {
            return new Promise((resolve) => {
                console.log('Trying alternative: clicking date in calendar...');
                
                // Look for the day in the calendar grid
                const dateElements = document.querySelectorAll('.ant-calendar-date');
                for (const dateEl of dateElements) {
                    if (dateEl.textContent.trim() === day && 
                        !dateEl.closest('.ant-calendar-next-month-btn-day') &&
                        !dateEl.closest('.ant-calendar-prev-month-btn-day')) {
                        
                        console.log('Clicking day:', day);
                        dateEl.click();
                        resolve();
                        return;
                    }
                }
                resolve();
            });
        }
    };

    console.log('🗓️ Ant DatePicker automation script loaded');

})();
