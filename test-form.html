<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Form - PageMate File Mode</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .form-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #4f46e5;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        input:focus, select:focus {
            border-color: #4f46e5;
            outline: none;
        }
        .submit-btn {
            background: #4f46e5;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 20px;
        }
        .submit-btn:hover {
            background: #3730a3;
        }
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        .field-info {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h1>📝 Employee Registration Form</h1>
        
        <div class="info">
            <h3>🎯 PageMate File Mode Test Form</h3>
            <p>This form is designed to test the Excel-to-form transfer functionality. Use this as the destination tab when testing PageMate's file mode.</p>
        </div>

        <form id="employeeForm">
            <div class="form-group">
                <label for="fullName">Full Name</label>
                <input type="text" id="fullName" name="fullName" placeholder="Enter full name">
                <div class="field-info">ID: fullName | Maps to Excel column: Name</div>
            </div>

            <div class="form-group">
                <label for="emailAddress">Email Address</label>
                <input type="email" id="emailAddress" name="emailAddress" placeholder="Enter email address">
                <div class="field-info">ID: emailAddress | Maps to Excel column: Email</div>
            </div>

            <div class="form-group">
                <label for="phoneNumber">Phone Number</label>
                <input type="tel" id="phoneNumber" name="phoneNumber" placeholder="Enter phone number">
                <div class="field-info">ID: phoneNumber | Maps to Excel column: Phone</div>
            </div>

            <div class="form-group">
                <label for="department">Department</label>
                <select id="department" name="department">
                    <option value="">Select Department</option>
                    <option value="Engineering">Engineering</option>
                    <option value="Marketing">Marketing</option>
                    <option value="Sales">Sales</option>
                    <option value="HR">HR</option>
                    <option value="Finance">Finance</option>
                </select>
                <div class="field-info">ID: department | Maps to Excel column: Department</div>
            </div>

            <div class="form-group">
                <label for="salary">Salary</label>
                <input type="number" id="salary" name="salary" placeholder="Enter salary">
                <div class="field-info">ID: salary | Maps to Excel column: Salary</div>
            </div>

            <div class="form-group">
                <label for="startDate">Start Date</label>
                <input type="date" id="startDate" name="startDate">
                <div class="field-info">ID: startDate | Maps to Excel column: Start Date</div>
            </div>

            <div class="form-group">
                <label for="employeeId">Employee ID</label>
                <input type="text" id="employeeId" name="employeeId" placeholder="Auto-generated">
                <div class="field-info">ID: employeeId | For testing auto-fill functionality</div>
            </div>

            <div class="form-group">
                <label for="notes">Notes</label>
                <input type="text" id="notes" name="notes" placeholder="Additional notes">
                <div class="field-info">ID: notes | For testing additional data</div>
            </div>

            <button type="submit" class="submit-btn">💾 Save Employee</button>
        </form>

        <div class="info" style="margin-top: 30px;">
            <h3>📋 Testing Instructions</h3>
            <ol>
                <li>Open PageMate extension</li>
                <li>Switch to Files mode in Settings</li>
                <li>Upload the sample Excel file in File Upload tab</li>
                <li>Go to Form Mapping tab</li>
                <li>Select this tab as destination</li>
                <li>Map Excel columns to form fields:
                    <ul>
                        <li>Name → #fullName</li>
                        <li>Email → #emailAddress</li>
                        <li>Phone → #phoneNumber</li>
                        <li>Department → #department</li>
                        <li>Salary → #salary</li>
                        <li>Start Date → #startDate</li>
                    </ul>
                </li>
                <li>Test the transfer functionality</li>
            </ol>
        </div>
    </div>

    <script>
        document.getElementById('employeeForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {};
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            console.log('Form submitted with data:', data);
            alert('✅ Form submitted successfully!\n\nData:\n' + JSON.stringify(data, null, 2));
        });

        // Auto-generate employee ID
        document.getElementById('employeeId').value = 'EMP' + Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    </script>
</body>
</html>
