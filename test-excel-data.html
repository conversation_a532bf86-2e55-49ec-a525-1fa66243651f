<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel File Test - PageMate</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #4f46e5;
            text-align: center;
            margin-bottom: 30px;
        }
        .info {
            background: #e0f2fe;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #0288d1;
        }
        .steps {
            background: #f3e5f5;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #7b1fa2;
        }
        .download-section {
            text-align: center;
            margin: 30px 0;
        }
        .download-btn {
            background: #4f46e5;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }
        .download-btn:hover {
            background: #3730a3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background: #4f46e5;
            color: white;
        }
        tr:nth-child(even) {
            background: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📁 PageMate Excel File Test</h1>
        
        <div class="info">
            <h3>🎯 Test the File Upload Feature</h3>
            <p>This page helps you test the new Excel file upload functionality in PageMate extension.</p>
        </div>

        <div class="steps">
            <h3>📋 How to Test:</h3>
            <ol>
                <li>Download the sample Excel file below</li>
                <li>Open PageMate extension</li>
                <li>Go to Settings → Transfer mode → Select "Files"</li>
                <li>Navigate to the File Upload tab (Variables tab transforms)</li>
                <li>Upload the Excel file by clicking or dragging</li>
                <li>Observe the file information, column names, and preview</li>
            </ol>
        </div>

        <div class="download-section">
            <h3>📥 Sample Excel Files</h3>
            <button class="download-btn" onclick="createSampleExcel()">
                📊 Create Sample Excel File
            </button>
            <p><small>Click to generate and download a sample Excel file for testing</small></p>
        </div>

        <h3>📊 Sample Data Structure</h3>
        <p>The generated Excel file will contain the following sample data:</p>
        
        <table>
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <th>Department</th>
                    <th>Salary</th>
                    <th>Start Date</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>John Doe</td>
                    <td><EMAIL></td>
                    <td>******-0123</td>
                    <td>Engineering</td>
                    <td>75000</td>
                    <td>2023-01-15</td>
                </tr>
                <tr>
                    <td>Jane Smith</td>
                    <td><EMAIL></td>
                    <td>******-0124</td>
                    <td>Marketing</td>
                    <td>65000</td>
                    <td>2023-02-20</td>
                </tr>
                <tr>
                    <td>Mike Johnson</td>
                    <td><EMAIL></td>
                    <td>******-0125</td>
                    <td>Sales</td>
                    <td>70000</td>
                    <td>2023-03-10</td>
                </tr>
                <tr>
                    <td>Sarah Wilson</td>
                    <td><EMAIL></td>
                    <td>******-0126</td>
                    <td>HR</td>
                    <td>60000</td>
                    <td>2023-04-05</td>
                </tr>
                <tr>
                    <td>David Brown</td>
                    <td><EMAIL></td>
                    <td>******-0127</td>
                    <td>Finance</td>
                    <td>80000</td>
                    <td>2023-05-12</td>
                </tr>
            </tbody>
        </table>

        <div class="info">
            <h3>✅ Expected Results</h3>
            <p>After uploading the Excel file, you should see:</p>
            <ul>
                <li><strong>File Information:</strong> Shows sheets count, rows, columns, and file size</li>
                <li><strong>Column Names:</strong> Beautiful chips showing: Name, Email, Phone, Department, Salary, Start Date</li>
                <li><strong>Data Preview:</strong> First 10 rows of data in a table format</li>
                <li><strong>Sheet Selector:</strong> Dropdown to switch between sheets (if multiple)</li>
            </ul>
        </div>
    </div>

    <!-- Include XLSX library for creating Excel files -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    
    <script>
        function createSampleExcel() {
            // Sample data
            const data = [
                ['Name', 'Email', 'Phone', 'Department', 'Salary', 'Start Date'],
                ['John Doe', '<EMAIL>', '******-0123', 'Engineering', 75000, '2023-01-15'],
                ['Jane Smith', '<EMAIL>', '******-0124', 'Marketing', 65000, '2023-02-20'],
                ['Mike Johnson', '<EMAIL>', '******-0125', 'Sales', 70000, '2023-03-10'],
                ['Sarah Wilson', '<EMAIL>', '******-0126', 'HR', 60000, '2023-04-05'],
                ['David Brown', '<EMAIL>', '******-0127', 'Finance', 80000, '2023-05-12'],
                ['Lisa Garcia', '<EMAIL>', '******-0128', 'Engineering', 78000, '2023-06-18'],
                ['Tom Anderson', '<EMAIL>', '******-0129', 'Marketing', 67000, '2023-07-22'],
                ['Emily Davis', '<EMAIL>', '******-0130', 'Sales', 72000, '2023-08-14'],
                ['Chris Miller', '<EMAIL>', '******-0131', 'HR', 62000, '2023-09-09'],
                ['Amanda Taylor', '<EMAIL>', '******-0132', 'Finance', 82000, '2023-10-03']
            ];

            // Create workbook
            const wb = XLSX.utils.book_new();
            
            // Create worksheet
            const ws = XLSX.utils.aoa_to_sheet(data);
            
            // Add worksheet to workbook
            XLSX.utils.book_append_sheet(wb, ws, 'Employee Data');
            
            // Create second sheet with different data
            const productData = [
                ['Product ID', 'Product Name', 'Category', 'Price', 'Stock', 'Supplier'],
                ['P001', 'Laptop Pro', 'Electronics', 1299.99, 45, 'TechCorp'],
                ['P002', 'Wireless Mouse', 'Electronics', 29.99, 120, 'AccessoryCo'],
                ['P003', 'Office Chair', 'Furniture', 199.99, 30, 'FurniturePlus'],
                ['P004', 'Desk Lamp', 'Furniture', 49.99, 75, 'LightingInc'],
                ['P005', 'Notebook Set', 'Stationery', 12.99, 200, 'PaperWorld']
            ];
            
            const ws2 = XLSX.utils.aoa_to_sheet(productData);
            XLSX.utils.book_append_sheet(wb, ws2, 'Products');
            
            // Generate Excel file and download
            XLSX.writeFile(wb, 'PageMate_Sample_Data.xlsx');
            
            alert('📊 Sample Excel file created and downloaded!\n\nFile: PageMate_Sample_Data.xlsx\nSheets: Employee Data, Products\n\nNow you can test the PageMate file upload feature!');
        }
    </script>
</body>
</html>
