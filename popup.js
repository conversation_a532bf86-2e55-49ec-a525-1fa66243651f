// Listen for auto transfer messages from background script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'autoTransferComplete') {
        console.log('Auto transfer completed:', request.result);
        // Show status message in popup if it's open
        if (request.result.successful > 0) {
            showStatus(`Auto transfer successful: ${request.result.successful}/${request.result.total} variables transferred`, 'success');
        } else {
            showStatus(`Auto transfer failed: ${request.result.failed}/${request.result.total} variables failed`, 'error');
        }
        sendResponse({ success: true });
    }
    return true;
});

document.addEventListener('DOMContentLoaded', function() {
    // Initialize variables arrays first
    let variableCounter = 0;
    let variables = [];
    let isElementHunting = false;

    // Helper function to get valid variables
    function getVariables() {
        return variables.filter(v => v.source && v.destination);
    }

    // Initialize sidebar navigation
    initializeSidebarNavigation();

    // Note: sourceInput and destinationInput are now dynamic variables
    const transferBtn = document.getElementById('transferBtn');
    const mainTransferBtn = document.getElementById('mainTransferBtn');
    const statusDiv = document.getElementById('status');
    const refreshTabsBtn = document.getElementById('refreshTabsBtn');
    const refreshSelectedBtn = document.getElementById('refreshSelectedBtn');
    const availableTabsList = document.getElementById('availableTabsList');
    const sourceTabSlot = document.getElementById('sourceTabSlot');
    const destinationTabSlot = document.getElementById('destinationTabSlot');

    // Store selected tabs
    let selectedSourceTab = null;
    let selectedDestinationTab = null;

    // Auto transfer state
    let autoTransferEnabled = false;

    // Sidebar Navigation
    function initializeSidebarNavigation() {
        const navBtns = document.querySelectorAll('.nav-btn');
        const pages = document.querySelectorAll('.page');

        // Remove existing event listeners by cloning and replacing buttons
        navBtns.forEach(btn => {
            const newBtn = btn.cloneNode(true);
            btn.parentNode.replaceChild(newBtn, btn);
        });

        // Re-query after replacement
        const newNavBtns = document.querySelectorAll('.nav-btn');

        newNavBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const targetPage = btn.getAttribute('data-page');
                const currentActivePage = document.querySelector('.page.active');

                // Clean up empty variables when leaving Variables tab (or file-upload in file mode)
                if (currentActivePage &&
                    (currentActivePage.id === 'variables-page' || currentActivePage.id === 'file-upload-page') &&
                    targetPage !== 'variables' && targetPage !== 'file-upload') {
                    if (typeof cleanupEmptyVariables === 'function') {
                        cleanupEmptyVariables();
                    }
                }

                // Remove active class from all nav buttons and pages
                newNavBtns.forEach(b => b.classList.remove('active'));
                pages.forEach(p => p.classList.remove('active'));

                // Add active class to clicked button and corresponding page
                btn.classList.add('active');
                const targetPageElement = document.getElementById(`${targetPage}-page`);
                if (targetPageElement) {
                    targetPageElement.classList.add('active');
                }
            });
        });
    }

    // Variables are now managed dynamically

    // Transfer button click handlers
    transferBtn.addEventListener('click', handleTransfer);
    if (mainTransferBtn) {
        mainTransferBtn.addEventListener('click', handleTransfer);
    }

    // Auto transfer button click handler
    const autoTransferBtn = document.getElementById('autoTransferBtn');
    if (autoTransferBtn) {
        autoTransferBtn.addEventListener('click', toggleAutoTransfer);
    }

    // Refresh tabs button click handler
    refreshTabsBtn.addEventListener('click', loadAvailableTabs);

    // Refresh selected tabs button click handler
    refreshSelectedBtn.addEventListener('click', refreshSelectedTabs);

    // Initialize drag and drop
    initializeDragAndDrop();

    // Load tabs on startup
    loadAvailableTabs();
    loadSavedTabSelections();

    // Initialize main page status
    updateMainPageStatus();

    // Load auto transfer state
    loadAutoTransferState();

    // Values are now saved automatically with dynamic variables

    function showStatus(message, type = 'info') {
        if (statusDiv) {
            statusDiv.textContent = message;
            statusDiv.className = `status-message ${type}`;

            if (type === 'success' || type === 'error') {
                setTimeout(() => {
                    statusDiv.textContent = '';
                    statusDiv.className = 'status-message';
                }, 3000);
            }
        }

        // Also show toast notification
        showToast(message, type);
    }

    function showToast(message, type = 'info') {
        const toast = document.getElementById('toast');
        const toastMessage = document.getElementById('toastMessage');

        if (toast && toastMessage) {
            toastMessage.textContent = message;
            toast.className = `toast ${type}`;
            toast.classList.add('show');

            setTimeout(() => {
                toast.classList.remove('show');
            }, 3000);
        }
    }

    function updateMainPageStatus() {
        // Update variables status
        const currentVariables = document.getElementById('currentVariables');
        if (currentVariables) {
            const validVariables = getVariables();
            if (validVariables.length > 0) {
                currentVariables.textContent = `${validVariables.length} variable${validVariables.length > 1 ? 's' : ''} configured`;
            } else {
                currentVariables.textContent = 'Not configured';
            }
        }

        // Update source tab status
        const currentSourceTab = document.getElementById('currentSourceTab');
        if (currentSourceTab) {
            if (selectedSourceTab) {
                currentSourceTab.textContent = selectedSourceTab.title;
            } else {
                currentSourceTab.textContent = 'Not selected';
            }
        }

        // Update destination tab status
        const currentDestinationTab = document.getElementById('currentDestinationTab');
        if (currentDestinationTab) {
            if (selectedDestinationTab) {
                currentDestinationTab.textContent = selectedDestinationTab.title;
            } else {
                currentDestinationTab.textContent = 'Not selected';
            }
        }

        // Update total transfers count
        updateTransferCount();
    }

    function updateTransferCount() {
        chrome.storage.local.get(['transferHistory'], function(result) {
            const history = result.transferHistory || [];
            const totalTransfers = document.getElementById('totalTransfers');
            const lastTransferValue = document.getElementById('lastTransferValue');

            if (totalTransfers) {
                totalTransfers.textContent = history.length;
            }

            if (lastTransferValue) {
                if (history.length > 0 && history[0].success) {
                    // Show the last successful transfer value
                    lastTransferValue.textContent = history[0].value || 'No value';
                } else {
                    lastTransferValue.textContent = 'No transfers yet';
                }
            }
        });
    }

    // Image Control Functions
    function showGifAnimation() {
        const statusImg = document.querySelector('#statusGif, .status-icon img');
        if (statusImg) {
            // Switch to animated GIF
            statusImg.src = 'icons/gif_icon.gif';
        }
    }

    function showStaticIcon() {
        const statusImg = document.querySelector('#statusGif, .status-icon img');
        if (statusImg) {
            // Switch to static PNG
            statusImg.src = 'icons/icon.png';
        }
    }

    // Auto Transfer Toggle Function
    function toggleAutoTransfer() {
        // Check if auto transfer should be allowed
        chrome.storage.local.get(['transferTrigger', 'buttonSelector', 'elementSelector'], (result) => {
            const triggerType = result.transferTrigger || 'manual';
            const buttonSelector = (result.buttonSelector || '').trim();
            const elementSelector = (result.elementSelector || '').trim();

            // If manual mode and no trigger values, don't allow auto mode
            if (triggerType === 'manual') {
                showStatus('Auto mode requires Button or Element trigger to be configured', 'error');
                return;
            }

            if (triggerType === 'button' && !buttonSelector) {
                showStatus('Please configure a button selector first', 'error');
                return;
            }

            if (triggerType === 'element' && !elementSelector) {
                showStatus('Please configure an element selector first', 'error');
                return;
            }

            // Toggle auto transfer
            autoTransferEnabled = !autoTransferEnabled;

            const autoTransferBtn = document.getElementById('autoTransferBtn');
            const autoStatus = autoTransferBtn.querySelector('.auto-status');

            if (autoTransferEnabled) {
                // Enable auto mode
                autoTransferBtn.classList.add('active');
                autoStatus.textContent = 'ON';
                autoTransferBtn.title = 'Disable Auto Transfer';
                showGifAnimation();
                showStatus('Auto transfer enabled', 'success');

                // Let background script handle trigger setup after storage is saved
                console.log('Auto transfer enabled - background script will handle setup');
            } else {
                // Disable auto mode
                autoTransferBtn.classList.remove('active');
                autoStatus.textContent = 'OFF';
                autoTransferBtn.title = 'Enable Auto Transfer';
                showStaticIcon();
                showStatus('Auto transfer disabled', 'info');

                // Let background script handle trigger stopping after storage is saved
                console.log('Auto transfer disabled - background script will handle cleanup');
            }

            // Save auto transfer state
            chrome.storage.local.set({ autoTransferEnabled: autoTransferEnabled }, () => {
                // Give a small delay to ensure storage is updated before background script processes
                if (autoTransferEnabled) {
                    setTimeout(() => {
                        console.log('Auto transfer state saved, triggers should be active');
                    }, 100);
                }
            });
        });
    }

    // Load Auto Transfer State Function
    function loadAutoTransferState() {
        chrome.storage.local.get(['autoTransferEnabled'], function(result) {
            autoTransferEnabled = result.autoTransferEnabled || false;

            const autoTransferBtn = document.getElementById('autoTransferBtn');
            const autoStatus = autoTransferBtn.querySelector('.auto-status');

            if (autoTransferEnabled) {
                // Set auto mode as enabled
                autoTransferBtn.classList.add('active');
                autoStatus.textContent = 'ON';
                autoTransferBtn.title = 'Disable Auto Transfer';
                showGifAnimation();

                // Setup auto triggers if enabled
                setupAutoTriggers();
            } else {
                // Set auto mode as disabled
                autoTransferBtn.classList.remove('active');
                autoStatus.textContent = 'OFF';
                autoTransferBtn.title = 'Enable Auto Transfer';
                showStaticIcon();
            }

            // Auto transfers are now handled directly in the background script
        });
    }

    // Auto transfers are now handled directly in the background script
    // No need to check for pending transfers

    // Auto Trigger Setup Functions
    function setupAutoTriggers() {
        chrome.storage.local.get(['transferTrigger', 'buttonSelector', 'elementSelector', 'selectedSourceTab'], (result) => {
            const triggerType = result.transferTrigger || 'manual';
            const buttonSelector = (result.buttonSelector || '').trim();
            const elementSelector = (result.elementSelector || '').trim();
            const selectedSourceTab = result.selectedSourceTab;

            if (triggerType === 'manual') {
                return;
            }

            const settings = {
                enabled: autoTransferEnabled,
                triggerType: triggerType,
                buttonSelector: buttonSelector,
                elementSelector: elementSelector
            };

            // Function to inject content script and setup trigger
            function injectAndSetupTrigger(tabId, tabTitle) {
                // First inject content script to ensure it's available
                chrome.scripting.executeScript({
                    target: { tabId: tabId },
                    files: ['content.js']
                }).then(() => {
                    // After injection, setup the trigger
                    chrome.tabs.sendMessage(tabId, {
                        action: 'setupAutoTrigger',
                        settings: settings
                    }, (response) => {
                        if (chrome.runtime.lastError) {
                            console.log(`Could not setup trigger on tab "${tabTitle}": ${chrome.runtime.lastError.message}`);
                        } else {
                            console.log(`Auto trigger setup successful on tab: "${tabTitle}"`);
                        }
                    });
                }).catch((error) => {
                    console.log(`Could not inject content script on tab "${tabTitle}":`, error);
                });
            }

            // Only setup triggers on source tab if one is selected
            if (selectedSourceTab) {
                // Setup trigger only on the selected source tab
                injectAndSetupTrigger(selectedSourceTab.id, selectedSourceTab.title);
            } else {
                // No specific source tab selected - setup on current active tab only
                chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                    if (tabs[0] && !tabs[0].url.startsWith('chrome://') && !tabs[0].url.startsWith('chrome-extension://')) {
                        injectAndSetupTrigger(tabs[0].id, tabs[0].title);
                    }
                });
            }
        });
    }

    function stopAutoTriggers() {
        // Update storage to disable auto transfer
        chrome.storage.local.set({ autoTransferEnabled: false }, () => {
            // Stop triggers in all tabs
            chrome.tabs.query({}, (tabs) => {
                tabs.forEach(tab => {
                    if (!tab.url.startsWith('chrome://') && !tab.url.startsWith('chrome-extension://')) {
                        chrome.tabs.sendMessage(tab.id, {
                            action: 'stopAutoTrigger'
                        }, () => {
                            // Ignore errors - tab might not have content script
                            if (chrome.runtime.lastError) {
                                // Silently ignore
                            }
                        });
                    }
                });
            });
        });
    }

    function parseSelector(selector) {
        // Parse format like "#username", ".email", "*name"
        // Also supports URL targeting: "#<EMAIL>"

        // Check for URL targeting first
        const urlMatch = selector.match(/^([#.*])([^@]+)@(.+)$/);
        if (urlMatch) {
            const [, selectorType, selectorValue, targetUrl] = urlMatch;
            return {
                elementType: null, // auto-detect
                selectorType,
                selectorValue,
                targetUrl
            };
        }

        // Check for standard format: selector only
        const match = selector.match(/^([#.*])(.+)$/);
        if (match) {
            const [, selectorType, selectorValue] = match;
            return {
                elementType: null, // auto-detect
                selectorType,
                selectorValue
            };
        }

        throw new Error('Invalid format');
    }

    function parseDestinationSelector(selector) {
        // Parse format like "#username", ".email", "*name"
        // Also supports URL targeting: "#<EMAIL>"
        const urlMatch = selector.match(/^([#.*])([^@]+)@(.+)$/);
        if (urlMatch) {
            const [, selectorType, selectorValue, targetUrl] = urlMatch;
            return { selectorType, selectorValue, targetUrl };
        }

        const match = selector.match(/^([#.*])(.+)$/);
        if (!match) {
            throw new Error('Invalid format');
        }

        const [, selectorType, selectorValue] = match;
        return { selectorType, selectorValue };
    }

    function validateVariables(transferMode = 'default') {
        const invalidFields = [];
        const validVariables = [];

        variables.forEach((variable, index) => {
            const source = variable.source ? variable.source.trim() : '';
            const destination = variable.destination ? variable.destination.trim() : '';

            // Check if both fields are filled
            if (!source || !destination) {
                if (!source && !destination) {
                    // Skip empty variables (they're allowed)
                    return;
                } else {
                    // One field filled, one empty - this is invalid
                    invalidFields.push(index + 1);
                    return;
                }
            }

            // Validate source format based on transfer mode
            let sourceValid = false;
            if (transferMode === 'tables') {
                // In table mode, source can be any text (table column header)
                sourceValid = source.length > 0;
            } else {
                // Default mode: #selector, .selector, *selector
                const sourcePattern = /^[#.*][a-zA-Z0-9_-]+(@.+)?$/;
                sourceValid = sourcePattern.test(source);
            }

            if (!sourceValid) {
                invalidFields.push(index + 1);
                return;
            }

            // Validate destination format: #selector, .selector, *selector, or selector@url
            const destinationPattern = /^[#.*][a-zA-Z0-9_-]+(@.+)?$/;
            if (!destinationPattern.test(destination)) {
                invalidFields.push(index + 1);
                return;
            }

            // If we get here, the variable is valid
            validVariables.push({
                source: source,
                destination: destination
            });
        });

        return {
            valid: invalidFields.length === 0 && validVariables.length > 0,
            invalidFields: invalidFields,
            validVariables: validVariables,
            hasVariables: validVariables.length > 0
        };
    }

    async function handleTransfer() {
        // Get transfer mode first
        const storage = await chrome.storage.local.get(['transferMode']);
        const transferMode = storage.transferMode || 'default';

        // Validate variables with transfer mode
        const validation = validateVariables(transferMode);

        if (!validation.hasVariables) {
            showStatus('Please configure at least one variable in the Variables tab', 'error');
            return;
        }

        // Don't stop if some variables are invalid - just skip them and show warning later

        // Process all valid variables
        const validVariables = validation.validVariables;

        // Add loading state to both transfer buttons
        transferBtn.classList.add('loading');
        transferBtn.disabled = true;
        if (mainTransferBtn) {
            mainTransferBtn.classList.add('loading');
            mainTransferBtn.disabled = true;
        }

        let successfulTransfers = 0;
        let failedTransfers = 0;
        let invalidFormatCount = validation.invalidFields.length;
        let transferResults = [];

        try {
            showStatus(`Starting transfer of ${validVariables.length} variable${validVariables.length > 1 ? 's' : ''}...`, 'info');

            // Process each variable
            for (let i = 0; i < validVariables.length; i++) {
                const variable = validVariables[i];
                const source = variable.source.trim();
                const destination = variable.destination.trim();

                // Variables to track transfer details for history
                let transferData = {
                    source: source,
                    destination: destination,
                    sourceTab: 'Unknown',
                    destinationTab: 'Unknown',
                    value: '',
                    success: false,
                    variableIndex: i + 1,
                    totalVariables: validVariables.length
                };

                try {
                    // Validate selectors based on transfer mode
                    const sourceSelector = transferMode === 'tables' ?
                        { selectorValue: source } :
                        parseSelector(source);
                    const destSelector = parseDestinationSelector(destination);

                    showStatus(`Processing variable ${i + 1}/${validVariables.length}: ${source} → ${destination}`, 'info');

                    // Get current tab
                    const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });

                    // Get source value with tab selection support
                    let sourceValue = null;
                    let sourceTabId = null;
                    let sourceTabInfo = null;

                    if (selectedSourceTab) {
                        // Use selected source tab - but get current tab info first
                        try {
                            const currentTabInfo = await chrome.tabs.get(selectedSourceTab.id);
                            sourceValue = await getSourceValue(selectedSourceTab.id, sourceSelector);
                            sourceTabId = selectedSourceTab.id;
                            sourceTabInfo = currentTabInfo;
                            transferData.sourceTab = currentTabInfo.title;
                        } catch (e) {
                            // Tab might have been closed or changed, try to find by title
                            const allTabs = await chrome.tabs.query({});
                            let foundTab = null;

                            for (const tab of allTabs) {
                                if (tab.title === selectedSourceTab.title || tab.url.includes('old-system.html')) {
                                    try {
                                        sourceValue = await getSourceValue(tab.id, sourceSelector);
                                        sourceTabId = tab.id;
                                        foundTab = tab;
                                        sourceTabInfo = tab;
                                        transferData.sourceTab = tab.title;
                                        break;
                                    } catch (err) {
                                        continue;
                                    }
                                }
                            }

                            if (!foundTab) {
                                throw new Error(`Source element not found. Selected tab "${selectedSourceTab.title}" may have been closed or changed.`);
                            }
                        }
                    } else if (sourceSelector.targetUrl) {
                        // URL-specific targeting for source
                        const allTabs = await chrome.tabs.query({});

                        for (const tab of allTabs) {
                            if (tab.url.includes(sourceSelector.targetUrl)) {
                                try {
                                    sourceValue = await getSourceValue(tab.id, sourceSelector);
                                    sourceTabId = tab.id;
                                    sourceTabInfo = tab;
                                    transferData.sourceTab = tab.title;
                                    break;
                                } catch (e) {
                                    console.log(`Source not found on specified tab "${tab.title}":`, e.message);
                                }
                            }
                        }
                    } else {
                        // Original behavior - try current tab first, then all tabs
                        try {
                            sourceValue = await getSourceValue(currentTab.id, sourceSelector);
                            sourceTabId = currentTab.id;
                            sourceTabInfo = currentTab;
                            transferData.sourceTab = currentTab.title;
                        } catch (error) {
                            // If not found on current tab, search all tabs
                            const allTabs = await chrome.tabs.query({});

                            for (const tab of allTabs) {
                                if (tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                                    continue; // Skip chrome internal pages
                                }

                                try {
                                    sourceValue = await getSourceValue(tab.id, sourceSelector);
                                    sourceTabId = tab.id;
                                    sourceTabInfo = tab;
                                    transferData.sourceTab = tab.title;
                                    break;
                                } catch (e) {
                                    // Continue searching other tabs
                                }
                            }
                        }
                    }

                    if (sourceValue === null || sourceValue === undefined) {
                        throw new Error(`Source element not found for variable ${i + 1}: ${source}`);
                    }

                    // Store the source value in transfer data
                    transferData.value = sourceValue;

                    // Set destination value with URL targeting support
                    let destinationSet = false;
                    let destinationTabInfo = null;

                    if (selectedDestinationTab) {
                        // Use selected destination tab - but get current tab info first
                        try {
                            const currentTabInfo = await chrome.tabs.get(selectedDestinationTab.id);
                            await setDestinationValue(selectedDestinationTab.id, destSelector, sourceValue);
                            destinationSet = true;
                            destinationTabInfo = currentTabInfo;
                            transferData.destinationTab = currentTabInfo.title;
                        } catch (e) {
                            // Tab might have been closed or changed, try to find by title
                            const allTabs = await chrome.tabs.query({});
                            let foundTab = null;

                            for (const tab of allTabs) {
                                if (tab.title === selectedDestinationTab.title || tab.url.includes('new-system.html')) {
                                    try {
                                        await setDestinationValue(tab.id, destSelector, sourceValue);
                                        destinationSet = true;
                                        foundTab = tab;
                                        destinationTabInfo = tab;
                                        transferData.destinationTab = tab.title;
                                        break;
                                    } catch (err) {
                                        continue;
                                    }
                                }
                            }

                            if (!foundTab) {
                                throw new Error(`Destination element not found. Selected tab "${selectedDestinationTab.title}" may have been closed or changed.`);
                            }
                        }
                    } else if (destSelector.targetUrl) {
                        // URL-specific targeting for destination
                        const allTabs = await chrome.tabs.query({});

                        for (const tab of allTabs) {
                            if (tab.url.includes(destSelector.targetUrl)) {
                                try {
                                    await setDestinationValue(tab.id, destSelector, sourceValue);
                                    destinationSet = true;
                                    destinationTabInfo = tab;
                                    transferData.destinationTab = tab.title;
                                    break;
                                } catch (e) {
                                    console.log(`Destination not found on specified tab "${tab.title}":`, e.message);
                                }
                            }
                        }
                    } else {
                        // Original behavior - try current tab first, then all tabs
                        try {
                            await setDestinationValue(currentTab.id, destSelector, sourceValue);
                            destinationSet = true;
                            destinationTabInfo = currentTab;
                            transferData.destinationTab = currentTab.title;
                        } catch (error) {
                            console.log('Destination not found on current tab:', error.message);
                            // If not found on current tab, search all tabs
                            const allTabs = await chrome.tabs.query({});

                            for (const tab of allTabs) {
                                if (tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                                    continue; // Skip chrome internal pages
                                }

                                try {
                                    await setDestinationValue(tab.id, destSelector, sourceValue);
                                    destinationSet = true;
                                    destinationTabInfo = tab;
                                    transferData.destinationTab = tab.title;
                                    break;
                                } catch (e) {
                                    console.log(`Destination not found on tab "${tab.title}":`, e.message);
                                    // Continue searching other tabs
                                }
                            }
                        }
                    }

                    if (!destinationSet) {
                        throw new Error(`Destination element not found for variable ${i + 1}: ${destination}`);
                    }

                    // Mark transfer as successful
                    transferData.success = true;
                    successfulTransfers++;

                    // Don't save individual transfers - we'll save one combined entry at the end
                    transferResults.push({
                        success: true,
                        variable: variable,
                        value: sourceValue,
                        sourceTab: transferData.sourceTab,
                        destinationTab: transferData.destinationTab
                    });

                } catch (error) {
                    console.error(`Transfer error for variable ${i + 1}:`, error);
                    failedTransfers++;

                    // Mark transfer as failed and save to history with error details
                    transferData.success = false;
                    transferData.error = error.message;
                    addToHistory(transferData);
                    transferResults.push({ success: false, variable: variable, error: error.message });
                }
            }

            // Show final status including invalid format count
            const totalAttempted = successfulTransfers + failedTransfers;
            const totalSkipped = invalidFormatCount;

            if (successfulTransfers > 0 && failedTransfers === 0 && totalSkipped === 0) {
                showStatus(`Successfully transferred ${successfulTransfers} value${successfulTransfers > 1 ? 's' : ''}`, 'success');
            } else if (successfulTransfers > 0 && (failedTransfers > 0 || totalSkipped > 0)) {
                let message = `Transferred ${successfulTransfers} value${successfulTransfers > 1 ? 's' : ''}`;
                if (failedTransfers > 0) {
                    message += `, failed ${failedTransfers} value${failedTransfers > 1 ? 's' : ''}`;
                }
                if (totalSkipped > 0) {
                    message += `, skipped ${totalSkipped} invalid format${totalSkipped > 1 ? 's' : ''}`;
                }
                showStatus(message, 'warning');
            } else if (totalSkipped > 0 && totalAttempted === 0) {
                showStatus(`All ${totalSkipped} variable${totalSkipped > 1 ? 's have' : ' has'} invalid format`, 'warning');
            } else {
                showStatus(`All ${failedTransfers} value${failedTransfers > 1 ? 's' : ''} failed to transfer`, 'error');
            }

            // Save one combined history entry if any transfers were successful
            if (successfulTransfers > 0) {
                const successfulVariables = [];
                transferResults.forEach((result, index) => {
                    if (result.success) {
                        successfulVariables.push(index + 1); // Variable numbers start from 1
                    }
                });

                const firstSuccessfulResult = transferResults.find(result => result.success);
                const firstSuccessfulValue = firstSuccessfulResult?.value || '';
                const sourceTabName = firstSuccessfulResult?.sourceTab || 'Unknown';
                const destinationTabName = firstSuccessfulResult?.destinationTab || 'Unknown';

                // Create combined history entry
                const combinedHistoryEntry = {
                    timestamp: new Date().toISOString(),
                    sourceTab: sourceTabName,
                    destinationTab: destinationTabName,
                    variableCount: successfulTransfers,
                    variables: successfulVariables,
                    skippedCount: invalidFormatCount,
                    failedCount: failedTransfers,
                    value: firstSuccessfulValue,
                    success: true
                };

                addToHistory(combinedHistoryEntry);
            }

        } catch (error) {
            console.error('Transfer process error:', error);
            showStatus(error.message, 'error');
        } finally {
            // Remove loading state from both transfer buttons
            transferBtn.classList.remove('loading');
            transferBtn.disabled = false;
            if (mainTransferBtn) {
                mainTransferBtn.classList.remove('loading');
                mainTransferBtn.disabled = false;
            }
        }
    }

    async function getSourceValue(tabId, selector) {
        return new Promise((resolve, reject) => {
            // Get transfer mode from storage
            chrome.storage.local.get(['transferMode'], (result) => {
                const transferMode = result.transferMode || 'default';

                chrome.tabs.sendMessage(tabId, {
                    action: 'getValue',
                    selector: selector,
                    transferMode: transferMode
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error('Could not connect to page. Please refresh and try again.'));
                        return;
                    }

                    if (response && response.success) {
                        resolve(response.value);
                    } else {
                        reject(new Error(response ? response.error : 'Failed to get source value'));
                    }
                });
            });
        });
    }

    async function setDestinationValue(tabId, selector, value) {
        return new Promise((resolve, reject) => {
            chrome.tabs.sendMessage(tabId, {
                action: 'setValue',
                selector: selector,
                value: value
            }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error('Could not connect to page. Please refresh and try again.'));
                    return;
                }

                if (response && response.success) {
                    resolve();
                } else {
                    reject(new Error(response ? response.error : 'Failed to set destination value'));
                }
            });
        });
    }

    async function loadAvailableTabs() {
        try {
            const tabs = await chrome.tabs.query({});
            const validTabs = tabs.filter(tab =>
                !tab.url.startsWith('chrome://') &&
                !tab.url.startsWith('chrome-extension://')
            );

            availableTabsList.innerHTML = validTabs.map(tab => {
                const url = new URL(tab.url);
                const domain = url.hostname;
                const path = url.pathname;
                const displayUrl = path && path !== '/' ? `${domain}${path}` : domain;
                return `
                    <div class="tab-item" draggable="true" data-tab-id="${tab.id}" data-tab-title="${tab.title}" data-tab-url="${tab.url}">
                        <div class="tab-title">${tab.title}</div>
                        <div class="tab-url">${displayUrl}</div>
                    </div>
                `;
            }).join('');

            if (validTabs.length === 0) {
                availableTabsList.innerHTML = '<div class="tab-item">No valid tabs found</div>';
            }

            // Re-attach drag event listeners
            attachDragListeners();

        } catch (error) {
            availableTabsList.innerHTML = '<div class="tab-item">Error loading tabs</div>';
        }
    }

    function initializeDragAndDrop() {
        // Add drop zone event listeners
        [sourceTabSlot, destinationTabSlot].forEach(slot => {
            slot.addEventListener('dragover', handleDragOver);
            slot.addEventListener('drop', handleDrop);
            slot.addEventListener('dragenter', handleDragEnter);
            slot.addEventListener('dragleave', handleDragLeave);
        });
    }

    function attachDragListeners() {
        const tabItems = availableTabsList.querySelectorAll('.tab-item[draggable="true"]');
        tabItems.forEach(item => {
            item.addEventListener('dragstart', handleDragStart);
            item.addEventListener('dragend', handleDragEnd);
        });
    }

    function handleDragStart(e) {
        e.dataTransfer.setData('text/plain', '');
        e.dataTransfer.effectAllowed = 'move';

        // Store tab data
        const tabData = {
            id: parseInt(e.target.dataset.tabId),
            title: e.target.dataset.tabTitle,
            url: e.target.dataset.tabUrl
        };
        e.dataTransfer.setData('application/json', JSON.stringify(tabData));

        e.target.classList.add('dragging');
    }

    function handleDragEnd(e) {
        e.target.classList.remove('dragging');
    }

    function handleDragOver(e) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
    }

    function handleDragEnter(e) {
        e.preventDefault();
        e.target.closest('.tab-container').classList.add('drag-over');
    }

    function handleDragLeave(e) {
        if (!e.target.closest('.tab-container').contains(e.relatedTarget)) {
            e.target.closest('.tab-container').classList.remove('drag-over');
        }
    }

    function handleDrop(e) {
        e.preventDefault();
        const container = e.target.closest('.tab-container');
        container.classList.remove('drag-over');

        try {
            const tabData = JSON.parse(e.dataTransfer.getData('application/json'));
            const slot = e.target.closest('.tab-slot');

            if (slot.id === 'sourceTabSlot') {
                setSourceTab(tabData);
            } else if (slot.id === 'destinationTabSlot') {
                setDestinationTab(tabData);
            }
        } catch (error) {
            console.error('Error handling drop:', error);
        }
    }

    function setSourceTab(tabData) {
        selectedSourceTab = tabData;
        sourceTabSlot.innerHTML = createSelectedTabHTML(tabData, 'source');
        attachRemoveButtonListener(sourceTabSlot);
        saveTabSelections();
        updateMainPageStatus();
    }

    function setDestinationTab(tabData) {
        selectedDestinationTab = tabData;
        destinationTabSlot.innerHTML = createSelectedTabHTML(tabData, 'destination');
        attachRemoveButtonListener(destinationTabSlot);
        saveTabSelections();
        updateMainPageStatus();
    }

    function attachRemoveButtonListener(container) {
        const removeBtn = container.querySelector('.remove-btn');
        if (removeBtn) {
            removeBtn.addEventListener('click', function() {
                const type = this.getAttribute('data-type');
                if (type === 'source') {
                    removeSourceTab();
                } else if (type === 'destination') {
                    removeDestinationTab();
                }
            });
        }
    }

    function createSelectedTabHTML(tabData, type) {
        const arrow = type === 'source' ? '📤' : '📥';
        const arrowText = type === 'source' ? 'FROM' : 'TO';
        return `
            <div class="selected-tab">
                <div class="tab-arrow">
                    <span class="arrow-icon">${arrow}</span>
                    <span class="arrow-text">${arrowText}</span>
                </div>
                <div class="selected-tab-title">${tabData.title}</div>
                <button class="remove-btn" data-type="${type}">×</button>
            </div>
        `;
    }

    function removeSourceTab() {
        selectedSourceTab = null;
        sourceTabSlot.innerHTML = `
            <div class="slot-placeholder">
                <span class="placeholder-icon">📤</span>
                <span class="placeholder-text">Drag source tab here</span>
            </div>
        `;
        saveTabSelections();
        updateMainPageStatus();
    }

    function removeDestinationTab() {
        selectedDestinationTab = null;
        destinationTabSlot.innerHTML = `
            <div class="slot-placeholder">
                <span class="placeholder-icon">📥</span>
                <span class="placeholder-text">Drag destination tab here</span>
            </div>
        `;
        saveTabSelections();
        updateMainPageStatus();
    }

    function saveTabSelections() {
        chrome.storage.local.set({
            selectedSourceTab: selectedSourceTab,
            selectedDestinationTab: selectedDestinationTab
        });
    }

    function loadSavedTabSelections() {
        chrome.storage.local.get(['selectedSourceTab', 'selectedDestinationTab'], function(result) {
            if (result.selectedSourceTab) {
                selectedSourceTab = result.selectedSourceTab;
                sourceTabSlot.innerHTML = createSelectedTabHTML(selectedSourceTab, 'source');
                attachRemoveButtonListener(sourceTabSlot);
            }
            if (result.selectedDestinationTab) {
                selectedDestinationTab = result.selectedDestinationTab;
                destinationTabSlot.innerHTML = createSelectedTabHTML(selectedDestinationTab, 'destination');
                attachRemoveButtonListener(destinationTabSlot);
            }
            updateMainPageStatus();
        });
    }

    async function refreshSelectedTabs() {
        try {
            const allTabs = await chrome.tabs.query({});
            let updated = false;

            // Update source tab if selected
            if (selectedSourceTab) {
                const currentTab = allTabs.find(tab =>
                    tab.id === selectedSourceTab.id ||
                    tab.title === selectedSourceTab.title ||
                    tab.url.includes('old-system.html')
                );

                if (currentTab) {
                    selectedSourceTab = {
                        id: currentTab.id,
                        title: currentTab.title,
                        url: currentTab.url
                    };
                    sourceTabSlot.innerHTML = createSelectedTabHTML(selectedSourceTab, 'source');
                    attachRemoveButtonListener(sourceTabSlot);
                    updated = true;
                }
            }

            // Update destination tab if selected
            if (selectedDestinationTab) {
                const currentTab = allTabs.find(tab =>
                    tab.id === selectedDestinationTab.id ||
                    tab.title === selectedDestinationTab.title ||
                    tab.url.includes('new-system.html')
                );

                if (currentTab) {
                    selectedDestinationTab = {
                        id: currentTab.id,
                        title: currentTab.title,
                        url: currentTab.url
                    };
                    destinationTabSlot.innerHTML = createSelectedTabHTML(selectedDestinationTab, 'destination');
                    attachRemoveButtonListener(destinationTabSlot);
                    updated = true;
                }
            }

            if (updated) {
                saveTabSelections();
                showStatus('Selected tabs updated successfully', 'success');
            } else {
                showStatus('No selected tabs to update', 'info');
            }

        } catch (error) {
            showStatus('Error updating selected tabs', 'error');
            console.error('Error refreshing selected tabs:', error);
        }
    }

    // Initialize page management
    initHistoryManagement();
    initSettingsManagement();
    initVariablesManagement();

    // Enhanced History Management
    function initHistoryManagement() {
        const clearHistoryBtn = document.getElementById('clearHistoryBtn');
        const exportHistoryBtn = document.getElementById('exportHistoryBtn');
        const historyFilter = document.getElementById('historyFilter');

        if (clearHistoryBtn) {
            clearHistoryBtn.addEventListener('click', clearHistory);
        }
        if (exportHistoryBtn) {
            exportHistoryBtn.addEventListener('click', exportHistory);
        }
        if (historyFilter) {
            historyFilter.addEventListener('change', filterHistory);
        }

        loadHistory();
        updateHistoryStats();
    }

    function updateHistoryStats() {
        chrome.storage.local.get(['transferHistory'], (result) => {
            const history = result.transferHistory || [];
            const totalElement = document.getElementById('historyTotal');
            if (totalElement) {
                totalElement.textContent = history.length;
            }
        });
    }

    function filterHistory() {
        const filter = document.getElementById('historyFilter').value;
        chrome.storage.local.get(['transferHistory'], (result) => {
            let history = result.transferHistory || [];

            switch (filter) {
                case 'success':
                    history = history.filter(h => h.success);
                    break;
                case 'failed':
                    history = history.filter(h => !h.success);
                    break;
                case 'today':
                    const today = new Date().toDateString();
                    history = history.filter(h => new Date(h.timestamp).toDateString() === today);
                    break;
                case 'week':
                    const weekAgo = new Date();
                    weekAgo.setDate(weekAgo.getDate() - 7);
                    history = history.filter(h => new Date(h.timestamp) >= weekAgo);
                    break;
            }

            displayHistory(history);
        });
    }

    function displayHistory(history) {
        const historyList = document.getElementById('historyList');

        if (history.length === 0) {
            historyList.innerHTML = `
                <div class="empty-state">
                    <span class="empty-icon">📝</span>
                    <p>No transfers found</p>
                    <small>Try adjusting the filter</small>
                </div>
            `;
            return;
        }

        historyList.innerHTML = history.map(entry => {
            if (entry.success) {
                // Success entry - show combined transfer details
                let variableText = '';
                if (entry.variables && entry.variables.length > 0) {
                    variableText = `Variables [${entry.variableCount || entry.variables.length} success]`;
                    if (entry.skippedCount > 0) {
                        variableText += ` [${entry.skippedCount} skipped]`;
                    }
                    if (entry.failedCount > 0) {
                        variableText += ` [${entry.failedCount} failed]`;
                    }
                } else {
                    variableText = `${entry.variableCount || 1} variable${(entry.variableCount || 1) > 1 ? 's' : ''} copied`;
                }

                // Determine icon and class based on whether there are skipped/failed items
                const hasIssues = (entry.skippedCount > 0) || (entry.failedCount > 0);
                const statusIcon = hasIssues ? '⚠️' : '✅';
                const itemClass = hasIssues ? 'warning' : 'success';

                return `
                    <div class="history-item ${itemClass}">
                        <div class="history-header">
                            <span class="history-status">${statusIcon}</span>
                            <span class="history-time">${formatTime(entry.timestamp)}</span>
                        </div>
                        <div class="history-details">
                            <div class="history-variables">${variableText}</div>
                            <div class="history-from">from: ${entry.sourceTab}</div>
                            <div class="history-to">to: ${entry.destinationTab}</div>
                            <div class="history-value">Value: "${entry.value}"</div>
                        </div>
                    </div>
                `;
            } else {
                // Failed entry - show error reason
                return `
                    <div class="history-item error">
                        <div class="history-header">
                            <span class="history-status">❌</span>
                            <span class="history-time">${formatTime(entry.timestamp)}</span>
                        </div>
                        <div class="history-details">
                            <div class="history-error">Transfer Failed</div>
                            <div class="history-error-reason">${entry.error || 'Unknown error'}</div>
                        </div>
                    </div>
                `;
            }
        }).join('');
    }

    function exportHistory() {
        chrome.storage.local.get(['transferHistory'], (result) => {
            const history = result.transferHistory || [];
            if (history.length === 0) {
                alert('No history to export');
                return;
            }

            const csvContent = [
                'Timestamp,Source,Destination,Source Tab,Destination Tab,Value,Success',
                ...history.map(entry =>
                    `"${entry.timestamp}","${entry.source}","${entry.destination}","${entry.sourceTab}","${entry.destinationTab}","${entry.value}","${entry.success}"`
                )
            ].join('\n');

            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `transfer-history-${new Date().toISOString().split('T')[0]}.csv`;
            a.click();
            URL.revokeObjectURL(url);
        });
    }

    function addToHistory(transfer) {
        chrome.storage.local.get(['transferHistory', 'autoSaveHistory'], (result) => {
            if (!result.autoSaveHistory) return;

            const history = result.transferHistory || [];
            const newEntry = {
                id: Date.now(),
                timestamp: transfer.timestamp || new Date().toISOString(),
                sourceTab: transfer.sourceTab,
                destinationTab: transfer.destinationTab,
                value: transfer.value,
                success: transfer.success,
                error: transfer.error || null,
                variableCount: transfer.variableCount || 1,
                variables: transfer.variables || [1],
                skippedCount: transfer.skippedCount || 0,
                failedCount: transfer.failedCount || 0
            };

            history.unshift(newEntry);

            // Limit history size
            chrome.storage.local.get(['historyLimit'], (limitResult) => {
                const limit = parseInt(limitResult.historyLimit) || 25;
                if (history.length > limit) {
                    history.splice(limit);
                }

                chrome.storage.local.set({ transferHistory: history }, () => {
                    loadHistory();
                    updateHistoryStats();
                });
            });
        });
    }

    function loadHistory() {
        chrome.storage.local.get(['transferHistory'], (result) => {
            const history = result.transferHistory || [];
            const historyList = document.getElementById('historyList');

            if (history.length === 0) {
                historyList.innerHTML = `
                    <div class="empty-state">
                        <span class="empty-icon">📝</span>
                        <p>No transfers yet</p>
                        <small>Your transfer history will appear here</small>
                    </div>
                `;
                return;
            }

            historyList.innerHTML = history.map(entry => {
                if (entry.success) {
                    // Success entry - show combined transfer details
                    let variableText = '';
                    if (entry.variables && entry.variables.length > 0) {
                        variableText = `Variables [${entry.variableCount || entry.variables.length} success]`;
                        if (entry.skippedCount > 0) {
                            variableText += ` [${entry.skippedCount} skipped]`;
                        }
                        if (entry.failedCount > 0) {
                            variableText += ` [${entry.failedCount} failed]`;
                        }
                    } else {
                        variableText = `${entry.variableCount || 1} variable${(entry.variableCount || 1) > 1 ? 's' : ''} copied`;
                    }

                    // Determine icon and class based on whether there are skipped/failed items
                    const hasIssues = (entry.skippedCount > 0) || (entry.failedCount > 0);
                    const statusIcon = hasIssues ? '⚠️' : '✅';
                    const itemClass = hasIssues ? 'warning' : 'success';

                    return `
                        <div class="history-item ${itemClass}">
                            <div class="history-header">
                                <span class="history-status">${statusIcon}</span>
                                <span class="history-time">${formatTime(entry.timestamp)}</span>
                            </div>
                            <div class="history-details">
                                <div class="history-variables">${variableText}</div>
                                <div class="history-from">from: ${entry.sourceTab}</div>
                                <div class="history-to">to: ${entry.destinationTab}</div>
                                <div class="history-value">Value: "${entry.value}"</div>
                            </div>
                        </div>
                    `;
                } else {
                    // Failed entry - show error reason
                    return `
                        <div class="history-item error">
                            <div class="history-header">
                                <span class="history-status">❌</span>
                                <span class="history-time">${formatTime(entry.timestamp)}</span>
                            </div>
                            <div class="history-details">
                                <div class="history-error">Transfer Failed</div>
                                <div class="history-error-reason">${entry.error || 'Unknown error'}</div>
                            </div>
                        </div>
                    `;
                }
            }).join('');
        });
    }

    function clearHistory() {
        if (confirm('Are you sure you want to clear all transfer history?')) {
            chrome.storage.local.set({ transferHistory: [] }, () => {
                loadHistory();
                updateHistoryStats();
            });
        }
    }

    function formatTime(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleString();
    }

    // Settings Management
    function initSettingsManagement() {
        loadSettings();

        // Save settings when changed
        const settingInputs = document.querySelectorAll('#settings-page input, #settings-page select');
        settingInputs.forEach(input => {
            input.addEventListener('change', saveSettings);
        });

        // Add special handler for transfer trigger dropdown
        const transferTriggerSelect = document.getElementById('transferTrigger');
        if (transferTriggerSelect) {
            transferTriggerSelect.addEventListener('change', (e) => {
                updateTriggerOptions(e.target.value);
                saveSettings();
            });
        }

        // Add special handlers for transfer mode radio buttons
        const transferModeRadios = document.querySelectorAll('input[name="transferMode"]');
        transferModeRadios.forEach(radio => {
            radio.addEventListener('change', (e) => {
                if (e.target.checked) {
                    updateModeInfo(e.target.value);
                    updateUIForTransferMode(e.target.value);
                    saveSettings();
                }
            });
        });
    }

    function loadSettings() {
        chrome.storage.local.get([
            'autoSaveHistory',
            'historyLimit',
            'transferTrigger',
            'buttonSelector',
            'elementSelector',
            'transferMode'
        ], (result) => {
            document.getElementById('autoSaveHistory').checked = result.autoSaveHistory !== false;
            document.getElementById('historyLimit').value = result.historyLimit || '25';
            document.getElementById('transferTrigger').value = result.transferTrigger || 'manual';
            document.getElementById('buttonSelector').value = result.buttonSelector || '';
            document.getElementById('elementSelector').value = result.elementSelector || '';

            // Set transfer mode
            const transferMode = result.transferMode || 'default';
            document.getElementById('transferModeDefault').checked = transferMode === 'default';
            document.getElementById('transferModeTables').checked = transferMode === 'tables';
            document.getElementById('transferModeFiles').checked = transferMode === 'files';

            // Show/hide trigger options based on current selection
            updateTriggerOptions(result.transferTrigger || 'manual');
            // Show/hide mode info based on current selection
            updateModeInfo(transferMode);
            // Update UI based on transfer mode
            updateUIForTransferMode(transferMode);
        });
    }

    function saveSettings() {
        const transferMode = document.querySelector('input[name="transferMode"]:checked').value;

        const settings = {
            autoSaveHistory: document.getElementById('autoSaveHistory').checked,
            historyLimit: document.getElementById('historyLimit').value,
            transferTrigger: document.getElementById('transferTrigger').value,
            buttonSelector: document.getElementById('buttonSelector').value,
            elementSelector: document.getElementById('elementSelector').value,
            transferMode: transferMode
        };

        chrome.storage.local.set(settings, () => {
            // If auto transfer is enabled, update the triggers
            if (autoTransferEnabled) {
                setupAutoTriggers();
            }

            // Update UI based on transfer mode
            updateUIForTransferMode(transferMode);
        });
    }

    function updateTriggerOptions(triggerType) {
        const manualOptions = document.getElementById('manualTriggerOptions');
        const buttonOptions = document.getElementById('buttonTriggerOptions');
        const elementOptions = document.getElementById('elementTriggerOptions');

        // Hide all options first
        if (manualOptions) manualOptions.style.display = 'none';
        if (buttonOptions) buttonOptions.style.display = 'none';
        if (elementOptions) elementOptions.style.display = 'none';

        // Show appropriate options based on selection
        switch (triggerType) {
            case 'manual':
                if (manualOptions) manualOptions.style.display = 'block';
                break;
            case 'button':
                if (buttonOptions) buttonOptions.style.display = 'block';
                break;
            case 'element':
                if (elementOptions) elementOptions.style.display = 'block';
                break;
            default:
                // Default to manual mode
                if (manualOptions) manualOptions.style.display = 'block';
                break;
        }
    }

    function updateModeInfo(transferMode) {
        const defaultModeInfo = document.getElementById('defaultModeInfo');
        const tablesModeInfo = document.getElementById('tablesModeInfo');
        const filesModeInfo = document.getElementById('filesModeInfo');

        // Hide all mode info first
        if (defaultModeInfo) defaultModeInfo.style.display = 'none';
        if (tablesModeInfo) tablesModeInfo.style.display = 'none';
        if (filesModeInfo) filesModeInfo.style.display = 'none';

        // Show the selected mode info
        switch (transferMode) {
            case 'default':
                if (defaultModeInfo) defaultModeInfo.style.display = 'block';
                break;
            case 'tables':
                if (tablesModeInfo) tablesModeInfo.style.display = 'block';
                break;
            case 'files':
                if (filesModeInfo) filesModeInfo.style.display = 'block';
                break;
            default:
                // Default to default mode
                if (defaultModeInfo) defaultModeInfo.style.display = 'block';
                break;
        }
    }

    // UI Transformation for Transfer Modes
    function updateUIForTransferMode(transferMode) {
        // Get navigation buttons
        const variablesBtn = document.querySelector('[data-page="variables"]');
        const configBtn = document.querySelector('[data-page="config"]');

        // Get main page action sections
        const defaultModeActions = document.getElementById('defaultModeActions');
        const fileModeActions = document.getElementById('fileModeActions');

        // Get file mode pages
        const fileUploadPage = document.getElementById('file-upload-page');
        const formMappingPage = document.getElementById('form-mapping-page');

        if (transferMode === 'files') {
            // File mode: Transform the UI

            // Update navigation button functionality for file mode
            if (variablesBtn) {
                variablesBtn.setAttribute('data-page', 'file-upload');
                variablesBtn.setAttribute('title', 'File Upload');
            }
            if (configBtn) {
                configBtn.setAttribute('data-page', 'form-mapping');
                configBtn.setAttribute('title', 'Form Mapping');
            }

            // Show file mode actions, hide default actions
            if (defaultModeActions) defaultModeActions.style.display = 'none';
            if (fileModeActions) fileModeActions.style.display = 'flex';

            // Initialize file mode functionality
            initFileMode();

        } else {
            // Default/Tables mode: Restore original UI

            // Restore navigation button functionality
            if (variablesBtn) {
                variablesBtn.setAttribute('data-page', 'variables');
                variablesBtn.setAttribute('title', 'Variables');
            }
            if (configBtn) {
                configBtn.setAttribute('data-page', 'config');
                configBtn.setAttribute('title', 'Tab Selection');
            }

            // Show default actions, hide file mode actions
            if (defaultModeActions) defaultModeActions.style.display = 'flex';
            if (fileModeActions) fileModeActions.style.display = 'none';

            // Hide file mode pages
            if (fileUploadPage) fileUploadPage.classList.remove('active');
            if (formMappingPage) formMappingPage.classList.remove('active');
        }

        // Re-initialize navigation to handle the new data-page attributes
        initializeSidebarNavigation();
    }

    // File Mode Initialization
    function initFileMode() {
        // This will be expanded later with file handling logic
        console.log('File mode initialized');

        // Initialize file upload functionality
        initFileUpload();
        initFormMapping();
    }

    function initFileUpload() {
        console.log('File upload initialized');

        const fileInput = document.getElementById('excelFileInput');
        const fileDropZone = document.getElementById('fileDropZone');
        const removeFileBtn = document.getElementById('removeFileBtn');

        if (!fileInput || !fileDropZone) {
            console.error('File upload elements not found');
            return;
        }

        // File input change handler
        fileInput.addEventListener('change', handleFileSelect);

        // Drop zone click handler
        fileDropZone.addEventListener('click', () => {
            fileInput.click();
        });

        // Drag and drop handlers
        fileDropZone.addEventListener('dragover', handleDragOver);
        fileDropZone.addEventListener('dragleave', handleDragLeave);
        fileDropZone.addEventListener('drop', handleFileDrop);

        // Remove file button handler
        if (removeFileBtn) {
            removeFileBtn.addEventListener('click', removeSelectedFile);
        }
    }

    // File handling functions
    let currentExcelData = null;
    let currentFileName = '';

    function handleFileSelect(event) {
        const file = event.target.files[0];
        if (file) {
            processExcelFile(file);
        }
    }

    function handleDragOver(event) {
        event.preventDefault();
        event.stopPropagation();
        event.target.closest('.file-drop-zone').classList.add('drag-over');
    }

    function handleDragLeave(event) {
        event.preventDefault();
        event.stopPropagation();
        event.target.closest('.file-drop-zone').classList.remove('drag-over');
    }

    function handleFileDrop(event) {
        event.preventDefault();
        event.stopPropagation();
        event.target.closest('.file-drop-zone').classList.remove('drag-over');

        const files = event.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            if (isExcelFile(file)) {
                processExcelFile(file);
            } else {
                showToast('Please select a valid Excel file (.xlsx or .xls)', 'error');
            }
        }
    }

    function isExcelFile(file) {
        const validTypes = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
            'application/vnd.ms-excel' // .xls
        ];
        const validExtensions = ['.xlsx', '.xls'];

        return validTypes.includes(file.type) ||
               validExtensions.some(ext => file.name.toLowerCase().endsWith(ext));
    }

    function processExcelFile(file) {
        showToast('Processing Excel file...', 'info');

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                // For now, we'll create a mock Excel data structure
                // This will be replaced with actual Excel parsing later
                const mockWorkbook = createMockExcelData(file);

                currentExcelData = mockWorkbook;
                currentFileName = file.name;

                displayFileInfo(file, mockWorkbook);
                displayColumns(mockWorkbook);
                displayPreview(mockWorkbook);

                showToast('Excel file loaded successfully! (Demo mode)', 'success');

            } catch (error) {
                console.error('Error processing Excel file:', error);
                showToast('Error processing Excel file. Please check the file format.', 'error');
            }
        };

        reader.onerror = function() {
            showToast('Error reading file. Please try again.', 'error');
        };

        reader.readAsArrayBuffer(file);
    }

    function createMockExcelData(file) {
        // Create a mock Excel workbook structure for demonstration
        const mockData = [
            ['Name', 'Email', 'Phone', 'Department', 'Salary', 'Start Date'],
            ['John Doe', '<EMAIL>', '******-0123', 'Engineering', '75000', '2023-01-15'],
            ['Jane Smith', '<EMAIL>', '******-0124', 'Marketing', '65000', '2023-02-20'],
            ['Mike Johnson', '<EMAIL>', '******-0125', 'Sales', '70000', '2023-03-10'],
            ['Sarah Wilson', '<EMAIL>', '******-0126', 'HR', '60000', '2023-04-05'],
            ['David Brown', '<EMAIL>', '******-0127', 'Finance', '80000', '2023-05-12'],
            ['Lisa Garcia', '<EMAIL>', '******-0128', 'Engineering', '78000', '2023-06-18'],
            ['Tom Anderson', '<EMAIL>', '******-0129', 'Marketing', '67000', '2023-07-22'],
            ['Emily Davis', '<EMAIL>', '******-0130', 'Sales', '72000', '2023-08-14'],
            ['Chris Miller', '<EMAIL>', '******-0131', 'HR', '62000', '2023-09-09']
        ];

        return {
            SheetNames: ['Employee Data'],
            Sheets: {
                'Employee Data': {
                    '!ref': 'A1:F10',
                    data: mockData
                }
            }
        };
    }

    function displayFileInfo(file, workbook) {
        // Show file info section
        const fileInfo = document.getElementById('fileInfo');
        const fileStats = document.getElementById('fileStats');

        if (fileInfo) {
            fileInfo.style.display = 'block';
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = formatFileSize(file.size);
        }

        if (fileStats) {
            fileStats.style.display = 'block';

            const sheetNames = workbook.SheetNames;
            const firstSheet = workbook.Sheets[sheetNames[0]];
            const data = firstSheet.data || [];

            document.getElementById('sheetCount').textContent = sheetNames.length;
            document.getElementById('rowCount').textContent = data.length;
            document.getElementById('columnCount').textContent = data.length > 0 ? data[0].length : 0;
            document.getElementById('fileSizeDisplay').textContent = formatFileSize(file.size);
        }
    }

    function displayColumns(workbook) {
        const columnsDisplay = document.getElementById('columnsDisplay');
        const columnsList = document.getElementById('columnsList');

        if (!columnsDisplay || !columnsList) return;

        // Get first sheet
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        const data = worksheet.data || [];

        // Get header row (first row)
        const headers = data.length > 0 ? data[0] : [];

        // Clear previous columns
        columnsList.innerHTML = '';

        // Create column chips
        headers.forEach((header, index) => {
            const chip = document.createElement('div');
            chip.className = 'column-chip';
            chip.textContent = header || `Column ${index + 1}`;
            chip.setAttribute('data-column-index', index);
            chip.setAttribute('data-column-name', header);

            // Add click handler for column selection
            chip.addEventListener('click', () => {
                chip.classList.toggle('selected');
            });

            columnsList.appendChild(chip);
        });

        columnsDisplay.style.display = 'block';
    }

    function displayPreview(workbook) {
        const filePreview = document.getElementById('filePreview');
        const sheetSelector = document.getElementById('sheetSelector');
        const previewTable = document.getElementById('previewTable');

        if (!filePreview || !sheetSelector || !previewTable) return;

        // Populate sheet selector
        sheetSelector.innerHTML = '';
        workbook.SheetNames.forEach(sheetName => {
            const option = document.createElement('option');
            option.value = sheetName;
            option.textContent = sheetName;
            sheetSelector.appendChild(option);
        });

        // Add sheet selector change handler
        sheetSelector.addEventListener('change', () => {
            updatePreviewTable(workbook, sheetSelector.value);
        });

        // Show initial preview
        updatePreviewTable(workbook, workbook.SheetNames[0]);
        filePreview.style.display = 'block';
    }

    function updatePreviewTable(workbook, sheetName) {
        const previewTable = document.getElementById('previewTable');
        if (!previewTable) return;

        const worksheet = workbook.Sheets[sheetName];
        const data = worksheet.data || [];

        // Limit preview to first 10 rows
        const previewData = data.slice(0, 10);

        if (previewData.length === 0) {
            previewTable.innerHTML = '<p>No data to preview</p>';
            return;
        }

        // Create table
        let tableHTML = '<table><thead><tr>';

        // Headers
        const headers = previewData[0] || [];
        headers.forEach(header => {
            tableHTML += `<th>${header || ''}</th>`;
        });
        tableHTML += '</tr></thead><tbody>';

        // Data rows (skip header row)
        for (let i = 1; i < previewData.length; i++) {
            tableHTML += '<tr>';
            const row = previewData[i] || [];
            for (let j = 0; j < headers.length; j++) {
                const cellValue = row[j] || '';
                tableHTML += `<td>${cellValue}</td>`;
            }
            tableHTML += '</tr>';
        }

        tableHTML += '</tbody></table>';
        previewTable.innerHTML = tableHTML;
    }

    function removeSelectedFile() {
        // Clear file input
        const fileInput = document.getElementById('excelFileInput');
        if (fileInput) {
            fileInput.value = '';
        }

        // Hide all file-related sections
        const fileInfo = document.getElementById('fileInfo');
        const fileStats = document.getElementById('fileStats');
        const columnsDisplay = document.getElementById('columnsDisplay');
        const filePreview = document.getElementById('filePreview');

        if (fileInfo) fileInfo.style.display = 'none';
        if (fileStats) fileStats.style.display = 'none';
        if (columnsDisplay) columnsDisplay.style.display = 'none';
        if (filePreview) filePreview.style.display = 'none';

        // Clear data
        currentExcelData = null;
        currentFileName = '';

        showToast('File removed', 'info');
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function initFormMapping() {
        // Form mapping logic will be added here
        console.log('Form mapping initialized');
    }

    // Variables Management
    function initVariablesManagement() {
        const addVariableBtn = document.getElementById('addVariableBtn');
        const variablesList = document.getElementById('variablesList');

        if (addVariableBtn) {
            addVariableBtn.addEventListener('click', addVariable);
        }

        // Event delegation for remove buttons and input changes
        if (variablesList) {
            variablesList.addEventListener('click', function(e) {
                if (e.target.classList.contains('remove-variable-btn')) {
                    const variableId = e.target.getAttribute('data-variable-id');
                    removeVariable(variableId);
                }
            });

            variablesList.addEventListener('input', function(e) {
                if (e.target.tagName === 'INPUT') {
                    const variableId = e.target.getAttribute('data-variable-id');
                    const field = e.target.getAttribute('data-field');
                    updateVariable(variableId, field, e.target.value);
                }
            });
        }

        // Load saved variables or add first variable
        loadVariables();
    }

    function addVariable(sourceValue = '', destinationValue = '') {
        variableCounter++;
        const variableId = `variable_${variableCounter}`;

        const variable = {
            id: variableId,
            source: sourceValue,
            destination: destinationValue
        };

        variables.push(variable);

        const variableHTML = `
            <div class="variable-item" data-variable-id="${variableId}">
                <div class="variable-header">
                    <div class="variable-number">Variable ${variableCounter}</div>
                    <button class="remove-variable-btn" data-variable-id="${variableId}">
                        🗑️ Remove
                    </button>
                </div>
                <div class="variable-inputs">
                    <div class="variable-input-group">
                        <label>Source Element</label>
                        <input type="text"
                               id="source_${variableId}"
                               value="${sourceValue}"
                               placeholder="#username"
                               data-variable-id="${variableId}"
                               data-field="source">
                    </div>
                    <div class="variable-input-group">
                        <label>Destination Element</label>
                        <input type="text"
                               id="destination_${variableId}"
                               value="${destinationValue}"
                               placeholder="#username"
                               data-variable-id="${variableId}"
                               data-field="destination">
                    </div>
                </div>
            </div>
        `;

        const variablesList = document.getElementById('variablesList');
        variablesList.insertAdjacentHTML('beforeend', variableHTML);

        // Save variables
        saveVariables();
        updateMainPageStatus();
    }

    function removeVariable(variableId) {
        // Remove from DOM
        const variableElement = document.querySelector(`[data-variable-id="${variableId}"]`);
        if (variableElement) {
            variableElement.remove();
        }

        // Remove from variables array
        variables = variables.filter(v => v.id !== variableId);

        // Renumber remaining variables
        renumberVariables();

        // Save variables
        saveVariables();
        updateMainPageStatus();
    }

    function updateVariable(variableId, field, value) {
        const variable = variables.find(v => v.id === variableId);
        if (variable) {
            variable[field] = value;
            saveVariables();
            updateMainPageStatus();
        }
    }

    function renumberVariables() {
        const variableElements = document.querySelectorAll('.variable-item');
        variableElements.forEach((element, index) => {
            const numberElement = element.querySelector('.variable-number');
            if (numberElement) {
                numberElement.textContent = `Variable ${index + 1}`;
            }
        });
    }

    function saveVariables() {
        chrome.storage.local.set({ variables: variables });
    }

    function cleanupEmptyVariables() {
        // Find variables with empty source or destination
        const emptyVariables = variables.filter(v =>
            !v.source || !v.source.trim() || !v.destination || !v.destination.trim()
        );

        if (emptyVariables.length > 0) {
            // Remove empty variables from DOM
            emptyVariables.forEach(variable => {
                const variableElement = document.querySelector(`[data-variable-id="${variable.id}"]`);
                if (variableElement) {
                    variableElement.remove();
                }
            });

            // Remove empty variables from array
            variables = variables.filter(v =>
                v.source && v.source.trim() && v.destination && v.destination.trim()
            );

            // If no variables left, add one empty variable
            if (variables.length === 0) {
                addVariable();
            } else {
                // Renumber remaining variables
                renumberVariables();
                // Save the cleaned variables
                saveVariables();
                updateMainPageStatus();
            }
        }
    }

    function loadVariables() {
        chrome.storage.local.get(['variables'], (result) => {
            const savedVariables = result.variables || [];

            // Clear current variables
            variables = [];
            variableCounter = 0;
            const variablesList = document.getElementById('variablesList');
            if (variablesList) {
                variablesList.innerHTML = '';
            }

            if (savedVariables.length === 0) {
                // Add first variable if none exist
                addVariable();
            } else {
                // Load saved variables
                savedVariables.forEach(variable => {
                    addVariable(variable.source, variable.destination);
                });
            }
        });
    }

    // ID Hunter functionality - Simple version that just copies element IDs
    function initializeElementHunter() {
        const elementHunterBtn = document.getElementById('elementHunterBtn');

        if (elementHunterBtn) {
            elementHunterBtn.addEventListener('click', toggleElementHunting);
        }
    }

    function toggleElementHunting() {
        if (isElementHunting) {
            stopElementHunting();
        } else {
            startElementHunting();
        }
    }

    function startElementHunting() {
        isElementHunting = true;
        updateElementHunterButton();
        showToast('Click on any element to copy its ID', 'info');

        // Send message to content script to start hunting
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            if (tabs[0]) {
                chrome.tabs.sendMessage(tabs[0].id, { action: 'startElementHunting' }, (response) => {
                    if (chrome.runtime.lastError) {
                        console.error('Error starting element hunting:', chrome.runtime.lastError);
                        showToast('Error: Could not start ID hunting. Please refresh the page.', 'error');
                        stopElementHunting();
                    } else {
                        // Close the extension popup automatically
                        setTimeout(() => {
                            window.close();
                        }, 500); // Small delay to show the toast first
                    }
                });
            }
        });
    }

    function stopElementHunting() {
        isElementHunting = false;
        updateElementHunterButton();

        // Send message to content script to stop hunting
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            if (tabs[0]) {
                chrome.tabs.sendMessage(tabs[0].id, { action: 'stopElementHunting' }, (response) => {
                    if (chrome.runtime.lastError) {
                        console.error('Error stopping element hunting:', chrome.runtime.lastError);
                    }
                });
            }
        });
    }

    function updateElementHunterButton() {
        const elementHunterBtn = document.getElementById('elementHunterBtn');
        if (elementHunterBtn) {
            if (isElementHunting) {
                elementHunterBtn.classList.add('active');
                elementHunterBtn.title = 'Stop ID Hunting';
                elementHunterBtn.style.backgroundColor = '#ef4444';
                elementHunterBtn.style.color = 'white';
            } else {
                elementHunterBtn.classList.remove('active');
                elementHunterBtn.title = 'ID Hunter';
                elementHunterBtn.style.backgroundColor = '';
                elementHunterBtn.style.color = '';
            }
        }
    }

    // Initialize element hunter when DOM is ready
    initializeElementHunter();

    // Functions are now handled via event delegation

});
